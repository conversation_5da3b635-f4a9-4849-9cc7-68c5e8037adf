#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检测系统代理设置
"""

import os
import socket
import requests
import winreg
import json

def detect_system_proxy():
    """检测系统代理设置"""
    print("=" * 60)
    print("系统代理设置检测")
    print("=" * 60)
    
    # 1. 检查环境变量
    print("\n🔍 检查环境变量:")
    proxy_vars = ['HTTP_PROXY', 'HTTPS_PROXY', 'http_proxy', 'https_proxy', 'ALL_PROXY', 'all_proxy']
    found_env_proxy = False
    for var in proxy_vars:
        value = os.environ.get(var)
        if value:
            print(f"   ✅ {var}: {value}")
            found_env_proxy = True
        else:
            print(f"   ❌ {var}: 未设置")
    
    if not found_env_proxy:
        print("   📝 未找到环境变量代理设置")
    
    # 2. 检查Windows注册表代理设置
    print("\n🔍 检查Windows系统代理设置:")
    try:
        with winreg.OpenKey(winreg.HKEY_CURRENT_USER, 
                           r"Software\Microsoft\Windows\CurrentVersion\Internet Settings") as key:
            try:
                proxy_enable = winreg.QueryValueEx(key, "ProxyEnable")[0]
                if proxy_enable:
                    proxy_server = winreg.QueryValueEx(key, "ProxyServer")[0]
                    print(f"   ✅ 系统代理已启用: {proxy_server}")
                else:
                    print(f"   ❌ 系统代理未启用")
            except FileNotFoundError:
                print(f"   ❌ 未找到代理设置")
    except Exception as e:
        print(f"   ❌ 读取注册表失败: {e}")
    
    # 3. 扫描常见代理端口
    print("\n🔍 扫描本地代理端口:")
    common_ports = [
        1080, 1081, 1082,  # SOCKS代理
        7890, 7891, 7892,  # Clash
        8080, 8081, 8082,  # HTTP代理
        10808, 10809,      # V2Ray
        2080, 2081, 2082,  # 其他
        3128, 3129,        # Squid
        9050, 9051,        # Tor
    ]
    
    active_ports = []
    for port in common_ports:
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(0.1)
            result = sock.connect_ex(('127.0.0.1', port))
            sock.close()
            if result == 0:
                active_ports.append(port)
                print(f"   ✅ 端口 {port} 活跃")
        except:
            pass
    
    if not active_ports:
        print("   ❌ 未检测到活跃的代理端口")
    
    # 4. 测试代理连接
    if active_ports:
        print(f"\n🧪 测试代理连接:")
        for port in active_ports:
            proxy_url = f"http://127.0.0.1:{port}"
            print(f"\n   测试端口 {port}:")
            
            # 测试HTTP代理
            try:
                response = requests.get(
                    "https://httpbin.org/ip",
                    proxies={"http": proxy_url, "https": proxy_url},
                    timeout=5
                )
                if response.status_code == 200:
                    ip_info = response.json()
                    print(f"     ✅ HTTP代理工作正常，IP: {ip_info.get('origin', 'unknown')}")
                else:
                    print(f"     ❌ HTTP代理返回状态码: {response.status_code}")
            except Exception as e:
                print(f"     ❌ HTTP代理测试失败: {e}")
            
            # 测试Google连接
            try:
                response = requests.get(
                    "https://www.google.com",
                    proxies={"http": proxy_url, "https": proxy_url},
                    timeout=10
                )
                if response.status_code == 200:
                    print(f"     ✅ 可以通过此代理访问Google")
                else:
                    print(f"     ❌ Google访问返回状态码: {response.status_code}")
            except Exception as e:
                print(f"     ❌ Google访问测试失败: {e}")
    
    # 5. 生成建议
    print(f"\n📋 建议:")
    if found_env_proxy:
        print("• 系统已设置环境变量代理，程序应该能自动使用")
    elif active_ports:
        working_ports = []
        for port in active_ports:
            # 简单测试端口是否可用于HTTPS
            try:
                proxy_url = f"http://127.0.0.1:{port}"
                response = requests.get(
                    "https://httpbin.org/ip",
                    proxies={"http": proxy_url, "https": proxy_url},
                    timeout=3
                )
                if response.status_code == 200:
                    working_ports.append(port)
            except:
                pass
        
        if working_ports:
            print(f"• 建议使用端口: {working_ports[0]}")
            print(f"• 在程序中设置代理: http://127.0.0.1:{working_ports[0]}")
        else:
            print("• 检测到活跃端口，但可能不是HTTP代理")
    else:
        print("• 未检测到代理设置，可能需要手动配置")
        print("• 请检查Hiddify等代理软件的设置")
    
    return active_ports

if __name__ == "__main__":
    detect_system_proxy()
