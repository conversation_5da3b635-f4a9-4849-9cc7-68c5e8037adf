# AI视频生成器 - 最小化依赖
# 用于快速安装和基础功能演示

# ==================== 核心GUI框架 ====================
PyQt5>=5.15.0
PyQtWebEngine>=5.15.0

# ==================== 基础网络库 ====================
aiohttp>=3.8.0
aiofiles>=0.8.0
requests>=2.28.0
httpx>=0.23.0

# ==================== 图像处理基础 ====================
Pillow>=9.0.0
numpy>=1.21.0

# ==================== 数据处理 ====================
pandas>=1.4.0
pyyaml>=6.0
json5>=0.9.0

# ==================== 文本处理 ====================
jieba>=0.42.0

# ==================== AI服务客户端 ====================
openai>=1.0.0
anthropic>=0.3.0

# ==================== 工具库 ====================
tqdm>=4.64.0
colorama>=0.4.5
click>=8.1.0

# ==================== 运行时支持 ====================
typing-extensions>=4.3.0
python-dotenv>=0.19.0

# ==================== 可选但推荐 ====================
# 视频处理 (如需视频功能)
# moviepy>=1.0.3
# ffmpeg-python>=0.2.0

# 音频处理 (如需语音功能)
# pydub>=0.25.1

# 机器学习 (如需本地AI功能)
# transformers>=4.21.0
# torch>=1.12.0

# 计算机视觉 (如需高级图像处理)
# opencv-python>=4.6.0

# ==================== 开发工具 (可选) ====================
# pytest>=7.0.0
# pytest-cov>=3.0.0
# black>=22.6.0
# flake8>=5.0.0
