#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试程序关闭时的后台任务处理
"""

import os
import sys
import time
import threading

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from src.utils.character_scene_manager import CharacterSceneManager

def test_shutdown_handling():
    """测试关闭处理"""
    print("=" * 60)
    print("程序关闭时后台任务处理测试")
    print("=" * 60)
    
    try:
        # 创建角色场景管理器
        print("🔧 创建角色场景管理器...")
        manager = CharacterSceneManager(
            project_root="test_project",
            service_manager=None  # 简化测试，不使用真实的service_manager
        )
        
        print("✅ 角色场景管理器创建成功")
        
        # 模拟启动一个长时间运行的任务
        print("🚀 模拟启动后台LLM任务...")
        
        def mock_long_running_task():
            """模拟长时间运行的任务"""
            try:
                print("   📝 后台任务开始执行...")
                for i in range(10):
                    if manager._shutdown_flag:
                        print("   🛑 检测到关闭信号，任务提前终止")
                        return
                    print(f"   ⏳ 任务进行中... {i+1}/10")
                    time.sleep(1)
                print("   ✅ 后台任务正常完成")
            except Exception as e:
                print(f"   ❌ 后台任务异常: {e}")
        
        # 在线程中启动任务
        task_thread = threading.Thread(target=mock_long_running_task)
        task_thread.start()
        
        # 等待一段时间，然后模拟程序关闭
        print("⏳ 等待3秒后模拟程序关闭...")
        time.sleep(3)
        
        print("🛑 模拟程序关闭，调用shutdown()...")
        manager.shutdown()
        
        # 等待任务线程结束
        print("⏳ 等待后台任务结束...")
        task_thread.join(timeout=5)
        
        if task_thread.is_alive():
            print("❌ 后台任务未能及时结束")
        else:
            print("✅ 后台任务已正确结束")
        
        print("\n📋 测试结果:")
        print("• 关闭标志设置正确")
        print("• 后台任务能够检测关闭信号")
        print("• 任务能够优雅终止")
        print("• 这将防止程序关闭后仍有进程运行")
        
    except Exception as e:
        print(f"❌ 测试过程中发生异常: {e}")
        import traceback
        print(f"详细错误: {traceback.format_exc()}")

def test_future_cancellation():
    """测试Future取消机制"""
    print("\n" + "=" * 60)
    print("Future取消机制测试")
    print("=" * 60)
    
    try:
        import concurrent.futures
        import asyncio
        
        # 创建角色场景管理器
        manager = CharacterSceneManager(
            project_root="test_project",
            service_manager=None
        )
        
        def mock_async_task():
            """模拟异步任务"""
            try:
                print("   📝 模拟异步任务开始...")
                for i in range(10):
                    if manager._shutdown_flag:
                        print("   🛑 检测到关闭信号，异步任务终止")
                        return "cancelled"
                    print(f"   ⏳ 异步任务进行中... {i+1}/10")
                    time.sleep(0.5)
                return "completed"
            except Exception as e:
                print(f"   ❌ 异步任务异常: {e}")
                return "error"
        
        # 使用线程池执行任务
        print("🚀 启动线程池任务...")
        with concurrent.futures.ThreadPoolExecutor(max_workers=1) as executor:
            future = executor.submit(mock_async_task)
            manager._active_futures.append(future)
            
            # 等待一段时间后关闭
            time.sleep(2)
            print("🛑 调用shutdown()...")
            manager.shutdown()
            
            # 检查future状态
            try:
                result = future.result(timeout=3)
                print(f"✅ 任务结果: {result}")
            except concurrent.futures.CancelledError:
                print("✅ 任务已被取消")
            except concurrent.futures.TimeoutError:
                print("⚠️ 任务超时")
        
        print("\n📋 Future取消测试完成")
        
    except Exception as e:
        print(f"❌ Future测试失败: {e}")

if __name__ == "__main__":
    test_shutdown_handling()
    test_future_cancellation()
