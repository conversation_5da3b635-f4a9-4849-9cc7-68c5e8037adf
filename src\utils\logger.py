#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强的日志系统

提供结构化日志、多级别输出、性能监控等功能：
- 结构化日志记录
- 多种输出格式（JSON、文本）
- 日志级别动态调整
- 性能监控和统计
- 异步日志写入
- 日志过滤和搜索
- 远程日志传输（可选）
"""

import logging
import logging.handlers
import os
import sys
import json
import time
import threading
import traceback
from datetime import datetime
from typing import Any, Dict, List, Optional, Union, Callable
from dataclasses import dataclass, field
from pathlib import Path
from enum import Enum
import asyncio
from concurrent.futures import ThreadPoolExecutor

# 获取项目根目录
PROJECT_ROOT = os.path.dirname(os.path.dirname(os.path.dirname(__file__)))
LOG_DIR = os.path.join(PROJECT_ROOT, 'logs')


class LogLevel(Enum):
    """日志级别枚举"""
    DEBUG = logging.DEBUG
    INFO = logging.INFO
    WARNING = logging.WARNING
    ERROR = logging.ERROR
    CRITICAL = logging.CRITICAL


class LogFormat(Enum):
    """日志格式枚举"""
    TEXT = "text"
    JSON = "json"
    STRUCTURED = "structured"


@dataclass
class LogConfig:
    """日志配置"""
    name: str = "AIVideoLogger"
    level: LogLevel = LogLevel.INFO
    format_type: LogFormat = LogFormat.STRUCTURED
    console_output: bool = True
    file_output: bool = True
    max_file_size: int = 10 * 1024 * 1024  # 10MB
    backup_count: int = 5
    async_logging: bool = True
    performance_monitoring: bool = True
    remote_url: Optional[str] = None
    custom_fields: Dict[str, Any] = field(default_factory=dict)

class StructuredFormatter(logging.Formatter):
    """结构化日志格式化器"""

    def __init__(self, format_type: LogFormat = LogFormat.STRUCTURED,
                 include_extra: bool = True):
        super().__init__()
        self.format_type = format_type
        self.include_extra = include_extra

    def format(self, record: logging.LogRecord) -> str:
        """格式化日志记录"""
        if self.format_type == LogFormat.JSON:
            return self._format_json(record)
        elif self.format_type == LogFormat.STRUCTURED:
            return self._format_structured(record)
        else:
            return self._format_text(record)

    def _format_json(self, record: logging.LogRecord) -> str:
        """JSON格式"""
        log_data = {
            'timestamp': datetime.fromtimestamp(record.created).isoformat(),
            'level': record.levelname,
            'logger': record.name,
            'module': record.module,
            'function': record.funcName,
            'line': record.lineno,
            'message': record.getMessage(),
            'thread': record.thread,
            'process': record.process
        }

        # 添加异常信息
        if record.exc_info:
            log_data['exception'] = self.formatException(record.exc_info)

        # 添加额外字段
        if self.include_extra:
            for key, value in record.__dict__.items():
                if key not in log_data and not key.startswith('_'):
                    log_data[key] = value

        return json.dumps(log_data, ensure_ascii=False)

    def _format_structured(self, record: logging.LogRecord) -> str:
        """结构化文本格式"""
        timestamp = datetime.fromtimestamp(record.created).strftime('%Y-%m-%d %H:%M:%S.%f')[:-3]

        parts = [
            f"[{timestamp}]",
            f"[{record.levelname:8}]",
            f"[{record.name}]",
            f"[{record.filename}:{record.lineno}]",
            f"[{record.funcName}]",
            f"[T:{record.thread}]",
            record.getMessage()
        ]

        # 添加异常信息
        if record.exc_info:
            parts.append(f"\n{self.formatException(record.exc_info)}")

        return " ".join(parts)

    def _format_text(self, record: logging.LogRecord) -> str:
        """简单文本格式"""
        timestamp = datetime.fromtimestamp(record.created).strftime('%Y-%m-%d %H:%M:%S')
        return f"[{timestamp}] [{record.levelname}] {record.getMessage()}"


class AsyncLogHandler(logging.Handler):
    """异步日志处理器"""

    def __init__(self, target_handler: logging.Handler, queue_size: int = 1000):
        super().__init__()
        self.target_handler = target_handler
        self.queue_size = queue_size
        self.log_queue = asyncio.Queue(maxsize=queue_size)
        self.executor = ThreadPoolExecutor(max_workers=1, thread_name_prefix="AsyncLog")
        self.running = True

        # 启动异步处理任务
        self._start_async_processing()

    def emit(self, record: logging.LogRecord):
        """发送日志记录"""
        try:
            if not self.log_queue.full():
                # 使用线程安全的方式添加到队列
                asyncio.create_task(self.log_queue.put(record))
            else:
                # 队列满时，直接同步处理
                self.target_handler.emit(record)
        except Exception:
            self.handleError(record)

    def _start_async_processing(self):
        """启动异步处理"""
        def process_logs():
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            loop.run_until_complete(self._process_log_queue())

        self.executor.submit(process_logs)

    async def _process_log_queue(self):
        """处理日志队列"""
        while self.running:
            try:
                # 等待日志记录
                record = await asyncio.wait_for(self.log_queue.get(), timeout=1.0)

                # 处理日志记录
                self.target_handler.emit(record)
                self.log_queue.task_done()

            except asyncio.TimeoutError:
                continue
            except Exception as e:
                print(f"异步日志处理错误: {e}", file=sys.stderr)

    def close(self):
        """关闭处理器"""
        self.running = False
        self.target_handler.close()
        self.executor.shutdown(wait=True)
        super().close()


class EnhancedLogger:
    """
    增强的日志记录器

    提供结构化日志、性能监控、异步处理等功能
    """

    def __init__(self, config: Optional[LogConfig] = None):
        """
        初始化增强日志记录器

        Args:
            config: 日志配置
        """
        self.config = config or LogConfig()
        self.logger = logging.getLogger(self.config.name)
        self.logger.setLevel(self.config.level.value)

        # 性能统计
        self.stats = {
            'total_logs': 0,
            'logs_by_level': {level.name: 0 for level in LogLevel},
            'start_time': time.time(),
            'last_log_time': None
        }

        # 线程安全锁
        self.lock = threading.Lock()

        # 初始化处理器
        self._setup_handlers()

        # 添加自定义字段
        if self.config.custom_fields:
            for key, value in self.config.custom_fields.items():
                setattr(self.logger, key, value)

    def _setup_handlers(self):
        """设置日志处理器"""
        # 清除现有处理器
        self.logger.handlers.clear()

        # 创建格式化器
        formatter = StructuredFormatter(self.config.format_type)

        # 文件处理器
        if self.config.file_output:
            self._setup_file_handler(formatter)

        # 控制台处理器
        if self.config.console_output:
            self._setup_console_handler(formatter)

    def _setup_file_handler(self, formatter: StructuredFormatter):
        """设置文件处理器"""
        try:
            # 确保日志目录存在
            log_dir = Path(LOG_DIR)
            log_dir.mkdir(parents=True, exist_ok=True)

            log_file = log_dir / f"{self.config.name.lower()}.log"

            # 创建轮转文件处理器
            file_handler = logging.handlers.RotatingFileHandler(
                log_file,
                maxBytes=self.config.max_file_size,
                backupCount=self.config.backup_count,
                encoding='utf-8'
            )
            file_handler.setLevel(self.config.level.value)
            file_handler.setFormatter(formatter)

            # 如果启用异步日志，包装处理器
            if self.config.async_logging:
                file_handler = AsyncLogHandler(file_handler)

            self.logger.addHandler(file_handler)

        except Exception as e:
            print(f"创建文件日志处理器失败: {e}", file=sys.stderr)

    def _setup_console_handler(self, formatter: StructuredFormatter):
        """设置控制台处理器"""
        try:
            console_handler = logging.StreamHandler(sys.stdout)
            console_handler.setLevel(self.config.level.value)

            # 控制台使用简化格式
            if self.config.format_type == LogFormat.JSON:
                console_formatter = StructuredFormatter(LogFormat.STRUCTURED)
            else:
                console_formatter = formatter

            console_handler.setFormatter(console_formatter)

            # 如果启用异步日志，包装处理器
            if self.config.async_logging:
                console_handler = AsyncLogHandler(console_handler)

            self.logger.addHandler(console_handler)

        except Exception as e:
            print(f"创建控制台日志处理器失败: {e}", file=sys.stderr)

    def _update_stats(self, level: str):
        """更新统计信息"""
        if self.config.performance_monitoring:
            with self.lock:
                self.stats['total_logs'] += 1
                self.stats['logs_by_level'][level] += 1
                self.stats['last_log_time'] = time.time()

    def debug(self, msg: str, **kwargs):
        """调试日志"""
        self._log(LogLevel.DEBUG, msg, **kwargs)

    def info(self, msg: str, **kwargs):
        """信息日志"""
        self._log(LogLevel.INFO, msg, **kwargs)

    def warning(self, msg: str, **kwargs):
        """警告日志"""
        self._log(LogLevel.WARNING, msg, **kwargs)

    def error(self, msg: str, **kwargs):
        """错误日志"""
        self._log(LogLevel.ERROR, msg, **kwargs)

    def critical(self, msg: str, **kwargs):
        """严重错误日志"""
        self._log(LogLevel.CRITICAL, msg, **kwargs)

    def exception(self, msg: str, **kwargs):
        """异常日志"""
        kwargs['exc_info'] = True
        self._log(LogLevel.ERROR, msg, **kwargs)

    def _log(self, level: LogLevel, msg: str, **kwargs):
        """内部日志方法"""
        self._update_stats(level.name)

        # 添加额外的上下文信息
        extra = kwargs.pop('extra', {})
        extra.update(self.config.custom_fields)

        # 记录日志
        self.logger.log(level.value, msg, extra=extra, **kwargs)

    def structured_log(self, level: LogLevel, event: str, **fields):
        """结构化日志记录"""
        log_data = {
            'event': event,
            'timestamp': datetime.now().isoformat(),
            **fields
        }

        msg = f"EVENT: {event}"
        if fields:
            msg += f" | DATA: {json.dumps(fields, ensure_ascii=False)}"

        self._log(level, msg, extra=log_data)

    def performance_log(self, operation: str, duration: float, **metadata):
        """性能日志"""
        self.structured_log(
            LogLevel.INFO,
            "performance_metric",
            operation=operation,
            duration_ms=round(duration * 1000, 2),
            **metadata
        )

    def get_stats(self) -> Dict[str, Any]:
        """获取日志统计信息"""
        with self.lock:
            runtime = time.time() - self.stats['start_time']
            return {
                **self.stats,
                'runtime_seconds': round(runtime, 2),
                'logs_per_second': round(self.stats['total_logs'] / runtime, 2) if runtime > 0 else 0
            }

    def set_level(self, level: LogLevel):
        """动态设置日志级别"""
        self.config.level = level
        self.logger.setLevel(level.value)
        for handler in self.logger.handlers:
            handler.setLevel(level.value)

    def flush(self):
        """刷新所有处理器"""
        for handler in self.logger.handlers:
            if hasattr(handler, 'flush'):
                handler.flush()

    def close(self):
        """关闭日志记录器"""
        for handler in self.logger.handlers:
            handler.close()
        self.logger.handlers.clear()


# 保持向后兼容的Logger类
class Logger:
    def __init__(self, name='AIVideoLogger', level=logging.DEBUG, fmt=None, remote_url=None, console_output=True):
        # 创建增强日志记录器配置
        config = LogConfig(
            name=name,
            level=LogLevel(level),
            format_type=LogFormat.STRUCTURED,
            console_output=console_output,
            async_logging=False  # 保持同步以兼容现有代码
        )

        self.enhanced_logger = EnhancedLogger(config)
        self.logger = self.enhanced_logger.logger

    def get_logger(self):
        return self.logger

    def debug(self, msg):
        self.enhanced_logger.debug(str(msg))

    def info(self, msg):
        self.enhanced_logger.info(str(msg))

    def warning(self, msg):
        self.enhanced_logger.warning(str(msg))

    def error(self, msg):
        self.enhanced_logger.error(str(msg))

    def critical(self, msg):
        self.enhanced_logger.critical(str(msg))

    def exception(self, msg):
        self.enhanced_logger.exception(str(msg))

    def flush(self):
        self.enhanced_logger.flush()


# 创建全局logger实例（保持向后兼容）
_logger_instance = Logger()
logger = _logger_instance.logger

# 为标准logger对象添加flush方法，以兼容现有代码
def _logger_flush():
    """为标准logger添加flush方法"""
    _logger_instance.flush()

# 动态添加flush方法到logger对象
setattr(logger, 'flush', _logger_flush)

# 创建增强日志记录器实例
enhanced_logger = EnhancedLogger()


# 便捷函数
def get_logger(name: str = "AIVideoLogger", config: Optional[LogConfig] = None) -> EnhancedLogger:
    """获取增强日志记录器实例"""
    if config is None:
        config = LogConfig(name=name)
    return EnhancedLogger(config)


def structured_log(level: LogLevel, event: str, **fields):
    """全局结构化日志"""
    enhanced_logger.structured_log(level, event, **fields)


def performance_log(operation: str, duration: float, **metadata):
    """全局性能日志"""
    enhanced_logger.performance_log(operation, duration, **metadata)