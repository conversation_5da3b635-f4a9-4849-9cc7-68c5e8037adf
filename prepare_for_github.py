#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI视频生成器 - GitHub准备脚本
准备项目文件以便上传到GitHub
"""

import os
import subprocess
import sys
import json
from pathlib import Path

def print_banner():
    """打印准备横幅"""
    print("=" * 60)
    print("📤 AI视频生成器 - GitHub准备工具")
    print("=" * 60)

def run_security_check():
    """运行安全检查"""
    print("🔐 运行安全检查...")
    
    try:
        result = subprocess.run([sys.executable, "security_check.py"],
                              capture_output=True, text=True)
        if result.returncode == 0:
            print("✅ 安全检查通过")
            return True
        else:
            print("❌ 安全检查失败:")
            if result.stdout:
                print(result.stdout)
            if result.stderr:
                print(result.stderr)
            return False
    except Exception as e:
        print(f"❌ 安全检查异常: {e}")
        return False

def run_cleanup():
    """运行项目清理"""
    print("🧹 清理项目文件...")
    
    try:
        result = subprocess.run([sys.executable, "cleanup.py"], 
                              capture_output=True, text=True)
        if result.returncode == 0:
            print("✅ 项目清理完成")
            return True
        else:
            print("⚠️ 项目清理失败，但继续准备")
            return True
    except Exception as e:
        print(f"❌ 项目清理异常: {e}")
        return False

def check_git_status():
    """检查Git状态"""
    print("📋 检查Git状态...")
    
    try:
        # 检查是否是Git仓库
        result = subprocess.run(["git", "status"], capture_output=True, text=True)
        if result.returncode == 0:
            print("✅ Git仓库状态:")
            print(result.stdout)
            return True
        else:
            print("⚠️ 不是Git仓库或Git不可用")
            return False
    except Exception as e:
        print(f"❌ Git检查异常: {e}")
        return False

def create_github_info():
    """创建GitHub信息文件"""
    print("📋 创建GitHub信息...")
    
    github_info = {
        "repository": {
            "name": "A2",
            "description": "🎬 AI视频生成器 - 功能强大的AI驱动视频生成工具",
            "topics": [
                "ai", "video-generation", "python", "pyqt5", 
                "llm", "image-generation", "text-to-video",
                "storyboard", "artificial-intelligence", "automation"
            ],
            "homepage": "",
            "language": "Python"
        },
        "features": [
            "🎨 五阶段分镜生成系统",
            "🤖 多AI服务支持 (DeepSeek, 通义千问, 智谱AI等)",
            "🖼️ 图像生成 (Pollinations, ComfyUI, DALL-E等)",
            "🎙️ 语音服务 (Azure TTS, OpenAI TTS等)",
            "💻 现代化PyQt5界面",
            "📁 完整的项目管理系统",
            "⚡ 异步处理架构",
            "🔧 模块化设计"
        ],
        "installation": {
            "requirements": [
                "Python 3.8+",
                "PyQt5 5.15+",
                "4GB+ RAM",
                "稳定网络连接"
            ],
            "quick_start": [
                "git clone https://github.com/your-username/A2.git",
                "cd A2",
                "python install.py",
                "python main.py"
            ]
        },
        "configuration": {
            "required": [
                "复制 config/app_settings.example.json 到 config/app_settings.json",
                "编辑配置文件添加API密钥",
                "至少配置一个LLM服务提供商"
            ],
            "supported_services": {
                "LLM": ["DeepSeek", "通义千问", "智谱AI", "Google Gemini", "OpenAI", "SiliconFlow"],
                "Image": ["Pollinations", "ComfyUI", "DALL-E", "Stability AI", "Google Imagen"],
                "Voice": ["Azure TTS", "ElevenLabs", "OpenAI TTS", "Edge TTS"]
            }
        }
    }
    
    try:
        with open("GITHUB_INFO.json", "w", encoding="utf-8") as f:
            json.dump(github_info, f, ensure_ascii=False, indent=2)
        print("✅ GitHub信息文件创建成功")
        return True
    except Exception as e:
        print(f"❌ GitHub信息文件创建失败: {e}")
        return False

def generate_upload_instructions():
    """生成上传说明"""
    print("📝 生成上传说明...")
    
    instructions = """# GitHub上传说明

## 🚀 上传步骤

### 1. 初始化Git仓库 (如果还没有)
```bash
git init
```

### 2. 添加远程仓库
```bash
git remote add origin https://github.com/your-username/A2.git
```

### 3. 添加所有文件
```bash
git add .
```

### 4. 提交更改
```bash
git commit -m "🎬 AI视频生成器 - 完整项目上传

✨ 功能特性:
- 五阶段分镜生成系统
- 多AI服务支持 (LLM/图像/语音)
- 现代化PyQt5界面
- 完整的项目管理
- 异步处理架构

🔧 技术栈:
- Python 3.8+
- PyQt5界面框架
- 多种AI服务集成
- 模块化架构设计"
```

### 5. 推送到GitHub
```bash
git push -u origin main
```

## 🔐 安全检查

✅ 已通过安全检查
✅ 敏感信息已排除
✅ 配置文件已保护

## 📋 仓库设置建议

### 仓库描述
```
🎬 AI视频生成器 - 功能强大的AI驱动视频生成工具，支持五阶段分镜生成、多AI服务集成、现代化界面设计
```

### 标签 (Topics)
```
ai, video-generation, python, pyqt5, llm, image-generation, text-to-video, storyboard, artificial-intelligence, automation
```

### README徽章建议
```markdown
[![Python](https://img.shields.io/badge/Python-3.8+-blue.svg)](https://python.org)
[![PyQt5](https://img.shields.io/badge/PyQt5-5.15+-green.svg)](https://pypi.org/project/PyQt5/)
[![License](https://img.shields.io/badge/License-MIT-yellow.svg)](LICENSE)
[![Stars](https://img.shields.io/github/stars/your-username/A2.svg)](https://github.com/your-username/A2/stargazers)
```

## 📁 项目结构
- 总文件数: ~127个
- 源代码: src/ 目录
- 配置文件: config/ 目录 (示例文件)
- 测试文件: tests/ 目录
- 文档文件: README.md, QUICK_START_NEW.md 等

## 🎯 下一步
1. 上传到GitHub
2. 设置仓库描述和标签
3. 创建Release版本
4. 添加GitHub Actions (可选)
5. 设置Issues和Discussions

## 📞 支持
- 问题反馈: GitHub Issues
- 功能请求: GitHub Discussions
- 文档: README.md 和相关文档文件
"""
    
    try:
        with open("UPLOAD_INSTRUCTIONS.md", "w", encoding="utf-8") as f:
            f.write(instructions)
        print("✅ 上传说明创建成功")
        return True
    except Exception as e:
        print(f"❌ 上传说明创建失败: {e}")
        return False

def show_project_summary():
    """显示项目摘要"""
    print("\n" + "=" * 60)
    print("📊 项目摘要")
    print("=" * 60)
    
    # 统计文件
    total_files = 0
    total_size = 0
    file_types = {}
    
    for root, dirs, files in os.walk("."):
        # 跳过特定目录
        dirs[:] = [d for d in dirs if d not in ['.git', 'venv', '__pycache__']]
        
        for file in files:
            file_path = os.path.join(root, file)
            try:
                file_size = os.path.getsize(file_path)
                total_files += 1
                total_size += file_size
                
                # 统计文件类型
                ext = Path(file).suffix.lower()
                file_types[ext] = file_types.get(ext, 0) + 1
                
            except (OSError, FileNotFoundError):
                pass
    
    size_mb = total_size / (1024 * 1024)
    
    print(f"📁 总文件数: {total_files}")
    print(f"📊 总大小: {size_mb:.2f} MB")
    
    print("\n📋 文件类型分布:")
    for ext, count in sorted(file_types.items(), key=lambda x: x[1], reverse=True)[:10]:
        ext_name = ext if ext else "无扩展名"
        print(f"   {ext_name:10}: {count:3} 个")

def main():
    """主准备流程"""
    print_banner()
    
    print("开始准备GitHub上传...\n")
    
    # 运行安全检查
    if not run_security_check():
        print("❌ 安全检查失败，请修复后重试")
        return False
    print()
    
    # 运行清理
    if not run_cleanup():
        print("❌ 项目清理失败")
        return False
    print()
    
    # 检查Git状态
    check_git_status()
    print()
    
    # 创建GitHub信息
    if not create_github_info():
        print("⚠️ GitHub信息创建失败，但继续准备")
    print()
    
    # 生成上传说明
    if not generate_upload_instructions():
        print("⚠️ 上传说明创建失败，但继续准备")
    print()
    
    # 显示项目摘要
    show_project_summary()
    
    print("\n" + "=" * 60)
    print("🎉 GitHub上传准备完成！")
    print("=" * 60)
    print("📋 准备内容:")
    print("✅ 安全检查通过")
    print("✅ 项目文件已清理")
    print("✅ GitHub信息已生成")
    print("✅ 上传说明已创建")
    print("\n📖 下一步:")
    print("1. 查看 UPLOAD_INSTRUCTIONS.md 获取详细上传步骤")
    print("2. 在GitHub上创建新仓库 (如果还没有)")
    print("3. 按照说明执行Git命令上传")
    print("4. 设置仓库描述和标签")
    print("\n🔐 隐私保护:")
    print("✅ API密钥已排除")
    print("✅ 敏感配置已保护")
    print("✅ 只包含示例配置文件")
    print("=" * 60)
    
    return True

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n❌ 准备被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 准备过程中发生错误: {e}")
        sys.exit(1)
