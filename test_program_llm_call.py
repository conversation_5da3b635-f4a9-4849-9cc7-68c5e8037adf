#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
模拟程序实际的LLM调用流程
"""

import os
import sys
import json

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from src.models.llm_api import LLMApi
from src.utils.config_manager import ConfigManager

def test_program_llm_flow():
    """测试程序实际的LLM调用流程"""
    print("=" * 60)
    print("模拟程序实际LLM调用流程测试")
    print("=" * 60)
    
    try:
        # 1. 初始化配置管理器（模拟程序启动）
        print("🔧 初始化配置管理器...")
        config_manager = ConfigManager("config")
        
        # 2. 获取模型配置（模拟五阶段分镜系统）
        print("📋 获取模型配置...")
        all_model_configs = config_manager.config.get("models", [])
        print(f"找到 {len(all_model_configs)} 个模型配置")
        
        # 3. 选择智谱AI模型（模拟用户选择）
        selected_model = "智谱AI"
        model_config = None
        for cfg in all_model_configs:
            if cfg.get("name") == selected_model:
                model_config = cfg
                break
        
        if not model_config:
            print(f"❌ 未找到模型 '{selected_model}' 的配置")
            return
        
        print(f"✅ 找到模型配置: {model_config['name']}")
        print(f"   类型: {model_config.get('type')}")
        print(f"   URL: {model_config.get('url')}")
        print(f"   密钥: {model_config.get('key', '')[:20]}...")
        
        # 4. 初始化LLM API（模拟程序初始化）
        print("\n🚀 初始化LLM API...")
        llm_api = LLMApi(
            api_type=model_config.get('type', 'deepseek'),
            api_key=model_config.get('key', ''),
            api_url=model_config.get('url', '')
        )
        
        print(f"✅ LLM API初始化成功")
        print(f"   API类型: {llm_api.api_type}")
        print(f"   分镜模型: {llm_api.shots_model_name}")
        print(f"   改写模型: {llm_api.rewrite_model_name}")
        
        # 5. 测试实际的API调用（模拟五阶段分镜调用）
        print("\n🎬 测试分镜生成调用...")
        
        # 模拟第一阶段的全局分析调用
        test_prompt = """请对以下故事进行全局分析：

三顾茅庐的故事：刘备为了请诸葛亮出山，三次前往隆中拜访。第一次诸葛亮不在家，第二次遇到大雪天气，第三次终于见到了诸葛亮，诸葛亮被刘备的诚意感动，答应出山辅佐。

请分析这个故事的主要人物、场景和情节结构。"""
        
        messages = [
            {"role": "system", "content": "你是一个专业的故事分析师，擅长分析故事结构和要素。"},
            {"role": "user", "content": test_prompt}
        ]
        
        result = llm_api._make_api_call(
            llm_api.shots_model_name,  # 使用程序实际使用的模型名称
            messages,
            "全局分析测试"
        )
        
        if isinstance(result, str) and not result.startswith("API请求失败"):
            print(f"✅ 分镜生成调用成功")
            print(f"   返回内容长度: {len(result)} 字符")
            print(f"   内容预览: {result[:200]}...")
        else:
            print(f"❌ 分镜生成调用失败: {result}")
            return
        
        # 6. 测试角色提取调用（模拟角色场景管理器）
        print("\n👥 测试角色提取调用...")
        
        character_prompt = """请从以下故事中提取主要角色信息：

三顾茅庐的故事：刘备为了请诸葛亮出山，三次前往隆中拜访。

请提取角色的姓名、身份、外貌特征等信息，以JSON格式返回。"""
        
        character_messages = [
            {"role": "system", "content": "你是一个专业的角色分析师，擅长从故事中提取角色信息。"},
            {"role": "user", "content": character_prompt}
        ]
        
        character_result = llm_api._make_api_call(
            llm_api.shots_model_name,
            character_messages,
            "角色提取测试"
        )
        
        if isinstance(character_result, str) and not character_result.startswith("API请求失败"):
            print(f"✅ 角色提取调用成功")
            print(f"   返回内容长度: {len(character_result)} 字符")
            print(f"   内容预览: {character_result[:200]}...")
        else:
            print(f"❌ 角色提取调用失败: {character_result}")
        
        print("\n" + "=" * 60)
        print("✅ 所有测试完成！智谱AI在程序中应该可以正常工作。")
        print("=" * 60)
        
    except Exception as e:
        print(f"❌ 测试过程中发生异常: {e}")
        import traceback
        print(f"详细错误: {traceback.format_exc()}")

if __name__ == "__main__":
    test_program_llm_flow()
