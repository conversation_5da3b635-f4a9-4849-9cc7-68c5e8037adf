#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试代理设置是否正确
"""

import os
import sys
import asyncio

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from src.services.llm_service import LLMService
from src.core.api_manager import APIManager
from src.utils.config_manager import ConfigManager

async def test_proxy_settings():
    """测试代理设置"""
    print("=" * 70)
    print("测试智能代理设置")
    print("=" * 70)
    
    try:
        # 初始化服务
        print("🔧 初始化服务...")
        config_manager = ConfigManager("config")
        api_manager = APIManager(config_manager)
        await api_manager.initialize()
        llm_service = LLMService(api_manager)
        
        # 测试国内API（应该禁用代理）
        print("\n🇨🇳 测试国内API（禁用代理）:")
        
        domestic_apis = [
            ("智谱AI", "zhipu"),
            ("通义千问", "tongyi"), 
            ("Deepseek", "deepseek")
        ]
        
        for name, provider in domestic_apis:
            print(f"\n🧪 测试 {name}...")
            try:
                result = await llm_service.execute(
                    provider=provider,
                    prompt="你好",
                    max_tokens=20,
                    temperature=0.7
                )
                
                if result.success:
                    print(f"✅ {name} 成功: {result.data['content'][:50]}...")
                else:
                    print(f"❌ {name} 失败: {result.error}")
            except Exception as e:
                print(f"❌ {name} 异常: {e}")
        
        # 测试国外API（应该使用系统代理）
        print(f"\n🌍 测试国外API（使用系统代理）:")
        print(f"   当前代理状态: Hiddify已连接到Japan 02")
        
        print(f"\n🧪 测试 Google Gemini...")
        try:
            result = await llm_service.execute(
                provider="google",
                prompt="Hello, please reply briefly",
                max_tokens=20,
                temperature=0.7
            )
            
            if result.success:
                print(f"✅ Google Gemini 成功: {result.data['content'][:50]}...")
                print(f"   ✅ 代理设置正确，可以访问Google服务")
            else:
                print(f"❌ Google Gemini 失败: {result.error}")
                if "location" in result.error.lower() or "region" in result.error.lower():
                    print(f"   ⚠️  可能是地区限制问题")
                elif "timeout" in result.error.lower() or "connect" in result.error.lower():
                    print(f"   ⚠️  可能是网络连接问题")
        except Exception as e:
            print(f"❌ Google Gemini 异常: {e}")
        
        # 测试故障转移
        print(f"\n🔄 测试故障转移机制...")
        result = await llm_service.execute_with_fallback(
            prompt="请简单介绍一下人工智能",
            max_tokens=100,
            temperature=0.7
        )
        
        if result.success:
            print(f"✅ 故障转移成功")
            print(f"   使用提供商: {result.metadata.get('provider', '未知')}")
            print(f"   响应内容: {result.data['content'][:100]}...")
        else:
            print(f"❌ 故障转移失败: {result.error}")
        
        print("\n" + "=" * 70)
        print("代理设置测试完成")
        print("=" * 70)
        print("📋 总结:")
        print("• 国内API（智谱AI、通义千问、Deepseek）禁用代理")
        print("• 国外API（Google Gemini）使用系统代理")
        print("• 这样可以同时访问国内外服务")
        
    except Exception as e:
        print(f"❌ 测试过程中发生异常: {e}")
        import traceback
        print(f"详细错误: {traceback.format_exc()}")

if __name__ == "__main__":
    asyncio.run(test_proxy_settings())
