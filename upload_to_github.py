#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI视频生成器 - GitHub上传脚本
安全地清理并上传项目到GitHub仓库
"""

import os
import subprocess
import sys
import json
from pathlib import Path

def print_banner():
    """打印上传横幅"""
    print("=" * 60)
    print("📤 AI视频生成器 - GitHub上传工具")
    print("=" * 60)

def check_git():
    """检查Git是否可用"""
    print("🔍 检查Git环境...")
    try:
        result = subprocess.run(["git", "--version"], capture_output=True, text=True)
        if result.returncode == 0:
            print(f"✅ Git可用: {result.stdout.strip()}")
            return True
        else:
            print("❌ Git不可用")
            return False
    except FileNotFoundError:
        print("❌ Git未安装")
        return False

def check_sensitive_files():
    """检查敏感文件"""
    print("🔐 检查敏感信息...")
    
    sensitive_patterns = [
        "config/app_settings.json",
        "config/llm_config.json", 
        "config/tts_config.json",
        "config/baidu_translate_config.py"
    ]
    
    found_sensitive = []
    
    for pattern in sensitive_patterns:
        if os.path.exists(pattern):
            # 检查文件内容是否包含真实API密钥
            try:
                with open(pattern, 'r', encoding='utf-8') as f:
                    content = f.read()
                    
                # 检查是否包含可能的真实API密钥
                suspicious_patterns = [
                    "sk-",  # OpenAI API密钥前缀
                    "Bearer sk-",  # Bearer token with API key
                    "AIza",  # Google API密钥前缀
                    "AKIA",  # AWS访问密钥前缀
                ]
                
                for sus_pattern in suspicious_patterns:
                    if sus_pattern in content and "YOUR_" not in content:
                        found_sensitive.append(pattern)
                        break
                        
            except Exception as e:
                print(f"⚠️ 无法检查文件 {pattern}: {e}")
    
    if found_sensitive:
        print("❌ 发现可能包含真实API密钥的文件:")
        for file in found_sensitive:
            print(f"   - {file}")
        print("请确保这些文件已被.gitignore排除或移除敏感信息")
        return False
    else:
        print("✅ 未发现敏感信息")
        return True

def clean_project():
    """清理项目"""
    print("🧹 清理项目...")
    
    try:
        # 运行清理脚本
        result = subprocess.run([sys.executable, "cleanup.py"], 
                              capture_output=True, text=True)
        if result.returncode == 0:
            print("✅ 项目清理完成")
            return True
        else:
            print(f"⚠️ 清理脚本执行失败: {result.stderr}")
            return False
    except Exception as e:
        print(f"❌ 清理失败: {e}")
        return False

def init_git_repo():
    """初始化Git仓库"""
    print("📁 初始化Git仓库...")
    
    if os.path.exists(".git"):
        print("✅ Git仓库已存在")
        return True
    
    try:
        result = subprocess.run(["git", "init"], capture_output=True, text=True)
        if result.returncode == 0:
            print("✅ Git仓库初始化成功")
            return True
        else:
            print(f"❌ Git仓库初始化失败: {result.stderr}")
            return False
    except Exception as e:
        print(f"❌ Git仓库初始化异常: {e}")
        return False

def add_remote_origin(repo_url):
    """添加远程仓库"""
    print(f"🔗 添加远程仓库: {repo_url}")
    
    try:
        # 检查是否已有origin
        result = subprocess.run(["git", "remote", "get-url", "origin"], 
                              capture_output=True, text=True)
        if result.returncode == 0:
            current_url = result.stdout.strip()
            if current_url == repo_url:
                print("✅ 远程仓库已正确配置")
                return True
            else:
                print(f"⚠️ 远程仓库URL不匹配，更新中...")
                subprocess.run(["git", "remote", "set-url", "origin", repo_url])
        else:
            # 添加新的origin
            result = subprocess.run(["git", "remote", "add", "origin", repo_url], 
                                  capture_output=True, text=True)
            if result.returncode == 0:
                print("✅ 远程仓库添加成功")
                return True
            else:
                print(f"❌ 远程仓库添加失败: {result.stderr}")
                return False
                
    except Exception as e:
        print(f"❌ 远程仓库配置异常: {e}")
        return False

def commit_and_push():
    """提交并推送代码"""
    print("📤 提交并推送代码...")
    
    try:
        # 添加所有文件
        print("   添加文件...")
        result = subprocess.run(["git", "add", "."], capture_output=True, text=True)
        if result.returncode != 0:
            print(f"❌ 添加文件失败: {result.stderr}")
            return False
        
        # 检查是否有变更
        result = subprocess.run(["git", "status", "--porcelain"], 
                              capture_output=True, text=True)
        if not result.stdout.strip():
            print("ℹ️ 没有需要提交的变更")
            return True
        
        # 提交变更
        print("   提交变更...")
        commit_message = "🎬 AI视频生成器 - 完整项目上传\n\n✨ 功能特性:\n- 五阶段分镜生成系统\n- 多AI服务支持 (LLM/图像/语音)\n- 现代化PyQt5界面\n- 完整的项目管理\n- 异步处理架构\n\n🔧 技术栈:\n- Python 3.8+\n- PyQt5界面框架\n- 多种AI服务集成\n- 模块化架构设计"
        
        result = subprocess.run(["git", "commit", "-m", commit_message], 
                              capture_output=True, text=True)
        if result.returncode != 0:
            print(f"❌ 提交失败: {result.stderr}")
            return False
        
        # 推送到远程仓库
        print("   推送到远程仓库...")
        result = subprocess.run(["git", "push", "-u", "origin", "main"], 
                              capture_output=True, text=True)
        if result.returncode != 0:
            # 尝试推送到master分支
            print("   尝试推送到master分支...")
            result = subprocess.run(["git", "push", "-u", "origin", "master"], 
                                  capture_output=True, text=True)
            if result.returncode != 0:
                print(f"❌ 推送失败: {result.stderr}")
                return False
        
        print("✅ 代码推送成功")
        return True
        
    except Exception as e:
        print(f"❌ 提交推送异常: {e}")
        return False

def create_release_info():
    """创建发布信息"""
    print("📋 创建发布信息...")
    
    release_info = {
        "version": "1.0.0",
        "release_date": "2025-06-17",
        "features": [
            "五阶段分镜生成系统",
            "多AI服务支持 (DeepSeek, 通义千问, 智谱AI等)",
            "图像生成 (Pollinations, ComfyUI, DALL-E等)",
            "语音服务 (Azure TTS, OpenAI TTS等)",
            "现代化PyQt5界面",
            "完整的项目管理系统",
            "异步处理架构",
            "模块化设计"
        ],
        "requirements": [
            "Python 3.8+",
            "PyQt5 5.15+",
            "4GB+ RAM",
            "稳定网络连接"
        ],
        "installation": [
            "git clone https://github.com/your-username/A2.git",
            "cd A2",
            "python install.py",
            "python main.py"
        ]
    }
    
    try:
        with open("RELEASE_INFO.json", "w", encoding="utf-8") as f:
            json.dump(release_info, f, ensure_ascii=False, indent=2)
        print("✅ 发布信息创建成功")
        return True
    except Exception as e:
        print(f"❌ 发布信息创建失败: {e}")
        return False

def show_upload_summary():
    """显示上传摘要"""
    print("\n" + "=" * 60)
    print("📊 上传摘要")
    print("=" * 60)
    
    # 统计文件数量
    total_files = 0
    total_size = 0
    
    for root, dirs, files in os.walk("."):
        # 跳过.git和venv目录
        dirs[:] = [d for d in dirs if d not in ['.git', 'venv', '__pycache__']]
        
        for file in files:
            file_path = os.path.join(root, file)
            try:
                file_size = os.path.getsize(file_path)
                total_files += 1
                total_size += file_size
            except (OSError, FileNotFoundError):
                pass
    
    size_mb = total_size / (1024 * 1024)
    
    print(f"📁 总文件数: {total_files}")
    print(f"📊 总大小: {size_mb:.2f} MB")
    print(f"🔐 敏感信息: 已保护")
    print(f"🧹 项目状态: 已清理")

def main():
    """主上传流程"""
    print_banner()
    
    # 获取仓库URL
    repo_url = input("请输入GitHub仓库URL (例: https://github.com/username/A2.git): ").strip()
    if not repo_url:
        print("❌ 仓库URL不能为空")
        return False
    
    print(f"\n🎯 目标仓库: {repo_url}")
    print("开始上传流程...\n")
    
    # 检查Git环境
    if not check_git():
        return False
    
    # 检查敏感信息
    if not check_sensitive_files():
        return False
    
    # 清理项目
    if not clean_project():
        print("⚠️ 清理失败，但继续上传...")
    
    # 初始化Git仓库
    if not init_git_repo():
        return False
    
    # 添加远程仓库
    if not add_remote_origin(repo_url):
        return False
    
    # 创建发布信息
    if not create_release_info():
        print("⚠️ 发布信息创建失败，但继续上传...")
    
    # 提交并推送
    if not commit_and_push():
        return False
    
    # 显示摘要
    show_upload_summary()
    
    print("\n" + "=" * 60)
    print("🎉 项目上传完成！")
    print("=" * 60)
    print("📋 上传内容:")
    print("✅ 完整的源代码")
    print("✅ 配置文件模板")
    print("✅ 安装和使用文档")
    print("✅ 测试套件")
    print("✅ 维护工具")
    print("\n🔐 隐私保护:")
    print("✅ API密钥已排除")
    print("✅ 敏感配置已保护")
    print("✅ 日志文件已排除")
    print("\n📖 下一步:")
    print("1. 在GitHub上查看仓库")
    print("2. 编辑README添加具体说明")
    print("3. 设置仓库描述和标签")
    print("4. 考虑创建Release版本")
    print("=" * 60)
    
    return True

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n❌ 上传被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 上传过程中发生错误: {e}")
        sys.exit(1)
