import os
import json

class ConfigManager:
    def __init__(self, config_dir=None):
        if config_dir is None:
            # 动态获取配置目录路径
            current_dir = os.path.dirname(os.path.abspath(__file__))
            project_root = os.path.dirname(os.path.dirname(current_dir))
            config_dir = os.path.join(project_root, 'config')
        self.config_dir = config_dir
        self.config_file = os.path.join(self.config_dir, 'llm_config.json')
        self.config_json_dir = os.path.join(self.config_dir, 'config.json')
        self.config = self._load_config()
        self.image_config = self._load_image_config()
        self.voice_config = self._load_voice_config()
        self.app_config = self._load_app_config()

    def _load_config(self):
        all_models = []
        # 只加载主配置文件，避免重复加载
        main_config_path = os.path.join(self.config_dir, 'llm_config.json')
        
        if os.path.exists(main_config_path):
            try:
                with open(main_config_path, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    # Check if the file contains a list of models under the 'models' key
                    if isinstance(data, dict) and "models" in data and isinstance(data["models"], list):
                        # 去重处理：基于模型名称和类型去重
                        seen_models = set()
                        for model in data["models"]:
                            model_key = (model.get("name", ""), model.get("type", ""))
                            if model_key not in seen_models and model.get("name"):
                                all_models.append(model)
                                seen_models.add(model_key)
                        print(f"Loaded {len(all_models)} unique models from main config")
                    else:
                        print(f"Warning: Main config file does not contain a valid models list.")
            except json.JSONDecodeError as e:
                print(f"Error decoding JSON from main config: {e}")
            except Exception as e:
                print(f"Error reading main config file: {e}")

        # If no config files are found, create a default one
        if not all_models:
             default_config_path = os.path.join(self.config_dir, 'llm_config.json')
             if not os.path.exists(default_config_path):
                default_config = {
                    "models": [
                        {
                            "name": "DefaultModel",
                            "type": "tongyi",
                            "key": "YOUR_API_KEY",
                            "url": "YOUR_API_URL"
                        }
                    ]
                }
                os.makedirs(self.config_dir, exist_ok=True)
                with open(default_config_path, 'w', encoding='utf-8') as f:
                    json.dump(default_config, f, indent=4)
                all_models.extend(default_config["models"])
                print(f"Created and loaded default config file: {default_config_path}")
             else:
                 # If default exists but no other models, load default
                 try:
                     with open(default_config_path, 'r', encoding='utf-8') as f:
                         default_config = json.load(f)
                         if isinstance(default_config, dict) and "models" in default_config and isinstance(default_config["models"], list):
                             all_models.extend(default_config["models"])
                             print(f"Loaded existing default config file: {default_config_path}")
                         elif isinstance(default_config, dict) and "name" in default_config:
                              all_models.append(default_config)
                              print(f"Loaded existing default config file (single model format): {default_config_path}")
                         else:
                             print(f"Warning: Existing default config file {default_config_path} does not contain a valid model configuration.")
                 except Exception as e:
                     print(f"Error reading default config {default_config_path}: {e}")

        return {"models": all_models}
    
    def _load_image_config(self):
        """加载图像生成配置"""
        image_config_path = os.path.join(self.config_json_dir, 'image_config.json')
        if os.path.exists(image_config_path):
            try:
                with open(image_config_path, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except Exception as e:
                print(f"Error loading image config: {e}")
        return {"image_generation": {"default_engine": "pollinations", "pollinations": {"enabled": True}}}
    
    def _load_voice_config(self):
        """加载语音配置"""
        voice_config_path = os.path.join(self.config_json_dir, 'voice_config.json')
        if os.path.exists(voice_config_path):
            try:
                with open(voice_config_path, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except Exception as e:
                print(f"Error loading voice config: {e}")
        return {"voice_generation": {"default_engine": "edge", "engines": {"edge_tts": {"enabled": True}}}}
    
    def _load_app_config(self):
        """加载应用配置"""
        app_config_path = os.path.join(self.config_json_dir, 'app_config.json')
        if os.path.exists(app_config_path):
            try:
                with open(app_config_path, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except Exception as e:
                print(f"Error loading app config: {e}")
        return {"app_settings": {"version": "2.0.0", "debug_mode": False}}

    def get_model_config(self, model_name):
        # This method remains the same, as it iterates through the 'models' list
        for model in self.config.get("models", []):
            if model.get("name") == model_name:
                return model
        return None

    def save_model_config(self, model_name, model_type, api_key, api_url):
        # Simple implementation: find and update, or add if not found
        models = self.config.get("models", [])
        found = False
        for model in models:
            if model.get("name") == model_name:
                model["type"] = model_type
                model["key"] = api_key
                model["url"] = api_url
                found = True
                break
        if not found:
            models.append({
                "name": model_name,
                "type": model_type,
                "key": api_key,
                "url": api_url
            })
        self.config["models"] = models
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(self.config, f, indent=4)
        except Exception as e:
            print(f"Error saving config to {self.config_file}: {e}")
    
    def save_app_settings(self, app_config):
        """保存应用设置到app_settings.json文件"""
        try:
            app_settings_file = os.path.join(self.config_dir, 'app_settings.json')
            
            # 如果文件存在，先读取现有设置
            existing_settings = {}
            if os.path.exists(app_settings_file):
                try:
                    with open(app_settings_file, 'r', encoding='utf-8') as f:
                        existing_settings = json.load(f)
                except Exception as e:
                    print(f"Warning: Could not read existing app settings: {e}")
            
            # 更新设置
            existing_settings.update(app_config)
            
            # 确保目录存在
            os.makedirs(self.config_dir, exist_ok=True)
            
            # 保存设置
            with open(app_settings_file, 'w', encoding='utf-8') as f:
                json.dump(existing_settings, f, indent=4, ensure_ascii=False)
            
            print(f"App settings saved to {app_settings_file}")
            return True
            
        except Exception as e:
            print(f"Error saving app settings: {e}")
            return False

    def save_config(self, config_data):
        """保存配置数据到 app_settings.json 文件。"""
        return self.save_app_settings(config_data)

    def get_models(self):
        return self.config.get("models", [])
    
    def get_image_config(self):
        """获取图像生成配置"""
        return self.image_config
    
    def get_voice_config(self):
        """获取语音配置"""
        return self.voice_config
    
    def get_app_config(self):
        """获取应用配置"""
        return self.app_config
    
    def get_llm_config(self):
        """获取LLM配置"""
        # 获取第一个启用的模型作为默认LLM配置
        for model in self.config.get("models", []):
            if model.get("enabled", True):  # 默认启用
                return {
                    "api_type": model.get("type"),
                    "api_key": model.get("key"),
                    "api_url": model.get("url"),
                    "model_name": model.get("model", model.get("name"))
                }
        return None
    
    def get_image_providers(self):
        """获取可用的图像生成提供商"""
        image_gen = self.image_config.get('image_generation', {})
        providers = []
        
        if image_gen.get('pollinations', {}).get('enabled', False):
            providers.append('pollinations')
        if image_gen.get('comfyui', {}).get('enabled', False):
            providers.append('comfyui')
        if image_gen.get('stability', {}).get('enabled', False):
            providers.append('stability')
        if image_gen.get('dalle', {}).get('enabled', False):
            providers.append('dalle')
            
        return providers
    
    def get_voice_providers(self):
        """获取可用的语音提供商"""
        engines = self.voice_config.get('voice_generation', {}).get('engines', {})
        providers = []
        
        if engines.get('edge_tts', {}).get('enabled', False):
            providers.append('edge_tts')
        if engines.get('siliconflow', {}).get('enabled', False):
            providers.append('siliconflow')
        if engines.get('openai_tts', {}).get('enabled', False):
            providers.append('openai_tts')
            
        return providers

    def get_model_by_name(self, name):
        for model in self.config.get("models", []):
            if model['name'] == name:
                return model
        return None

    def add_model(self, model):
        if "models" not in self.config:
            self.config["models"] = []
        self.config["models"].append(model)
        self._save_config()

    def remove_model(self, name):
        if "models" in self.config:
            self.config["models"] = [model for model in self.config["models"] if model['name'] != name]
            self._save_config()

    def update_model(self, name, updated_model):
        if "models" in self.config:
            for i, model in enumerate(self.config["models"]):
                if model['name'] == name:
                    self.config["models"][i] = updated_model
                    break
            self._save_config()

    def _save_config(self):
        with open(self.config_file, 'w', encoding='utf-8') as f:
            json.dump(self.config, f, indent=4)
    
    # TTS相关配置管理
    def get_tts_config(self):
        """获取TTS配置"""
        tts_config_file = os.path.join(self.config_dir, 'tts_config.json')
        if os.path.exists(tts_config_file):
            try:
                with open(tts_config_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except Exception as e:
                print(f"加载TTS配置失败: {e}")
        
        # 返回默认TTS配置
        return self._get_default_tts_config()
    
    def _get_default_tts_config(self):
        """获取默认TTS配置"""
        return {
            'default_engine': 'edge',
            'default_voice': 'zh-CN-XiaoxiaoNeural-Female',
            'default_rate': 1.0,
            'default_volume': 1.0,
            'generate_subtitle': True,
            'output_format': 'mp3',
            'edge_tts': {
                'enabled': True
            },
            'siliconflow': {
                'enabled': False,
                'api_key': '',
                'base_url': 'https://api.siliconflow.cn/v1'
            },
            'audio': {
                'output_dir': 'audio',
                'subtitle_dir': 'subtitles'
            }
        }
    
    def save_tts_config(self, config):
        """保存TTS配置"""
        tts_config_file = os.path.join(self.config_dir, 'tts_config.json')
        try:
            os.makedirs(self.config_dir, exist_ok=True)
            with open(tts_config_file, 'w', encoding='utf-8') as f:
                json.dump(config, f, ensure_ascii=False, indent=2)
        except Exception as e:
            print(f"保存TTS配置失败: {e}")
    
    def save_llm_config(self, config):
        """保存LLM配置"""
        try:
            # 确保配置目录存在
            os.makedirs(self.config_dir, exist_ok=True)
            
            # 更新内存中的配置
            if 'models' in config:
                if 'models' not in self.config:
                    self.config['models'] = []
                
                # 合并新的模型配置
                for model_name, model_info in config['models'].items():
                    # 查找是否已存在同名模型（包括占位符版本）
                    existing_indices = []
                    for i, model in enumerate(self.config['models']):
                        if model.get('name') == model_name:
                            existing_indices.append(i)
                    
                    # 构建模型配置
                    model_config = {
                        'name': model_name,
                        'type': model_info.get('type', 'openai'),
                        'key': model_info.get('api_key', ''),
                        'url': model_info.get('base_url', '')
                    }
                    
                    # 删除所有同名的现有模型（包括占位符版本）
                    for i in reversed(existing_indices):
                        del self.config['models'][i]
                    
                    # 只有当API密钥不是占位符时才添加新配置
                    api_key = model_config.get('key', '')
                    if api_key and not api_key.startswith('YOUR_') and not api_key.endswith('_HERE'):
                        self.config['models'].append(model_config)
                    else:
                        print(f"跳过保存模型 {model_name}：API密钥为空或为占位符")
            
            # 保存到文件
            self._save_config()
            return True
            
        except Exception as e:
            print(f"保存LLM配置失败: {e}")
            return False
    
    def get_tts_setting(self, key, default=None):
        """获取TTS配置项"""
        config = self.get_tts_config()
        keys = key.split('.')
        value = config
        
        try:
            for k in keys:
                value = value[k]
            return value
        except (KeyError, TypeError):
            return default
    
    def set_tts_setting(self, key, value):
        """设置TTS配置项"""
        config = self.get_tts_config()
        keys = key.split('.')
        
        # 导航到目标位置
        current = config
        for k in keys[:-1]:
            if k not in current:
                current[k] = {}
            current = current[k]
        
        # 设置值
        current[keys[-1]] = value
        
        # 保存配置
        self.save_tts_config(config)
    
    def get_audio_output_dir(self):
        """获取音频输出目录"""
        output_dir = self.get_tts_setting('audio.output_dir', 'audio')
        # 确保output_dir是字符串
        if not isinstance(output_dir, str):
            output_dir = str(output_dir)

        # 转换为绝对路径
        if not os.path.isabs(output_dir):
            base_dir = os.path.dirname(os.path.dirname(os.path.dirname(__file__)))
            output_dir = os.path.join(base_dir, output_dir)

        # 确保目录存在
        if not os.path.exists(output_dir):
            os.makedirs(output_dir, exist_ok=True)

        return output_dir
    
    def get_subtitle_output_dir(self):
        """获取字幕输出目录"""
        output_dir = self.get_tts_setting('audio.subtitle_dir', 'subtitles')
        # 确保output_dir是字符串
        if not isinstance(output_dir, str):
            output_dir = str(output_dir)

        # 转换为绝对路径
        if not os.path.isabs(output_dir):
            base_dir = os.path.dirname(os.path.dirname(os.path.dirname(__file__)))
            output_dir = os.path.join(base_dir, output_dir)

        # 确保目录存在
        if not os.path.exists(output_dir):
            os.makedirs(output_dir, exist_ok=True)

        return output_dir
    
    def get_setting(self, key, default=None):
        """获取应用配置项"""
        config = self.get_app_config()
        keys = key.split('.')
        value = config
        
        try:
            for k in keys:
                value = value[k]
            return value
        except (KeyError, TypeError):
            return default
    
    def set_setting(self, key, value):
        """设置应用配置项"""
        config = self.get_app_config()
        keys = key.split('.')
        
        # 导航到目标位置
        current = config
        for k in keys[:-1]:
            if k not in current:
                current[k] = {}
            current = current[k]
        
        # 设置值
        current[keys[-1]] = value
        
        # 保存配置
        self.app_config = config
        app_config_path = os.path.join(self.config_json_dir, 'app_config.json')
        try:
            os.makedirs(os.path.dirname(app_config_path), exist_ok=True)
            with open(app_config_path, 'w', encoding='utf-8') as f:
                json.dump(config, f, ensure_ascii=False, indent=2)
        except Exception as e:
            print(f"Error saving app config: {e}")


# ============================================================================
# 增强的配置管理器
# ============================================================================

from typing import Any, Dict, List, Optional, Union, Callable
from dataclasses import dataclass, field
from pathlib import Path
import threading
import time
from copy import deepcopy


@dataclass
class ConfigSchema:
    """配置模式定义"""
    name: str
    required_fields: List[str] = field(default_factory=list)
    optional_fields: Dict[str, Any] = field(default_factory=dict)
    validators: Dict[str, Callable] = field(default_factory=dict)

    def validate(self, config: Dict[str, Any]) -> bool:
        """验证配置"""
        # 检查必需字段
        for field in self.required_fields:
            if field not in config:
                print(f"配置 {self.name} 缺少必需字段: {field}")
                return False

        # 运行自定义验证器
        for field, validator in self.validators.items():
            if field in config:
                try:
                    if not validator(config[field]):
                        print(f"配置 {self.name} 字段 {field} 验证失败")
                        return False
                except Exception as e:
                    print(f"配置 {self.name} 字段 {field} 验证器执行失败: {e}")
                    return False

        return True


class EnhancedConfigManager:
    """
    增强的配置管理器

    提供类型安全、验证和热重载功能的配置管理
    """

    def __init__(self, config_dir: Optional[str] = None):
        """
        初始化配置管理器

        Args:
            config_dir: 配置目录路径，可选
        """
        if config_dir is None:
            current_dir = os.path.dirname(os.path.abspath(__file__))
            project_root = os.path.dirname(os.path.dirname(current_dir))
            config_dir = os.path.join(project_root, 'config')

        self.config_dir = Path(config_dir)
        self.config_dir.mkdir(parents=True, exist_ok=True)

        # 配置存储
        self.configs: Dict[str, Dict[str, Any]] = {}
        self.schemas: Dict[str, ConfigSchema] = {}
        self.file_timestamps: Dict[str, float] = {}

        # 线程安全
        self.lock = threading.RLock()

        # 变更监听器
        self.change_listeners: Dict[str, List[Callable]] = {}

        # 环境变量前缀
        self.env_prefix = "A2_"

        # 初始化默认配置模式
        self._init_default_schemas()

        # 加载所有配置
        self._load_all_configs()

        # 启动热重载监控
        self._start_hot_reload()

    def _init_default_schemas(self):
        """初始化默认配置模式"""
        # LLM配置模式
        self.register_schema(ConfigSchema(
            name="llm",
            required_fields=["models"],
            validators={
                "models": lambda x: isinstance(x, list) and len(x) > 0
            }
        ))

        # 图像配置模式
        self.register_schema(ConfigSchema(
            name="image",
            required_fields=["image_generation"],
            validators={
                "image_generation": lambda x: isinstance(x, dict) and "default_engine" in x
            }
        ))

        # 语音配置模式
        self.register_schema(ConfigSchema(
            name="voice",
            required_fields=["voice_generation"],
            validators={
                "voice_generation": lambda x: isinstance(x, dict) and "default_engine" in x
            }
        ))

        # 应用配置模式
        self.register_schema(ConfigSchema(
            name="app",
            required_fields=["app_settings"],
            validators={
                "app_settings": lambda x: isinstance(x, dict) and "version" in x
            }
        ))

    def register_schema(self, schema: ConfigSchema):
        """注册配置模式"""
        with self.lock:
            self.schemas[schema.name] = schema

    def get_config(self, name: str, reload: bool = False) -> Optional[Dict[str, Any]]:
        """
        获取配置

        Args:
            name: 配置名称
            reload: 是否强制重新加载

        Returns:
            Dict[str, Any]: 配置数据
        """
        with self.lock:
            if reload or name not in self.configs:
                self._load_config(name)

            config = self.configs.get(name)
            if config:
                # 应用环境变量覆盖
                config = self._apply_env_overrides(name, config)

            return deepcopy(config) if config else None

    def set_config(self, name: str, config: Dict[str, Any], save: bool = True) -> bool:
        """
        设置配置

        Args:
            name: 配置名称
            config: 配置数据
            save: 是否保存到文件

        Returns:
            bool: 是否成功
        """
        try:
            with self.lock:
                # 验证配置
                if name in self.schemas:
                    if not self.schemas[name].validate(config):
                        return False

                old_config = self.configs.get(name)
                self.configs[name] = deepcopy(config)

                # 保存到文件
                if save:
                    self._save_config(name, config)

                # 触发变更监听器
                self._notify_change_listeners(name, old_config, config)

                return True

        except Exception as e:
            print(f"设置配置失败 {name}: {e}")
            return False

    def get_setting(self, config_name: str, key: str, default: Any = None) -> Any:
        """
        获取配置项

        Args:
            config_name: 配置名称
            key: 配置键（支持点分隔的嵌套键）
            default: 默认值

        Returns:
            Any: 配置值
        """
        config = self.get_config(config_name)
        if not config:
            return default

        keys = key.split('.')
        value = config

        try:
            for k in keys:
                value = value[k]
            return value
        except (KeyError, TypeError):
            return default

    def set_setting(self, config_name: str, key: str, value: Any, save: bool = True) -> bool:
        """
        设置配置项

        Args:
            config_name: 配置名称
            key: 配置键（支持点分隔的嵌套键）
            value: 配置值
            save: 是否保存

        Returns:
            bool: 是否成功
        """
        config = self.get_config(config_name) or {}
        keys = key.split('.')

        # 导航到目标位置
        current = config
        for k in keys[:-1]:
            if k not in current:
                current[k] = {}
            current = current[k]

        # 设置值
        current[keys[-1]] = value

        return self.set_config(config_name, config, save)

    def add_change_listener(self, config_name: str, listener: Callable):
        """添加配置变更监听器"""
        with self.lock:
            if config_name not in self.change_listeners:
                self.change_listeners[config_name] = []
            self.change_listeners[config_name].append(listener)

    def remove_change_listener(self, config_name: str, listener: Callable):
        """移除配置变更监听器"""
        with self.lock:
            if config_name in self.change_listeners:
                try:
                    self.change_listeners[config_name].remove(listener)
                except ValueError:
                    pass

    def _load_all_configs(self):
        """加载所有配置文件"""
        config_files = [
            ("llm", "llm_config.json"),
            ("image", "config.json/image_config.json"),
            ("voice", "config.json/voice_config.json"),
            ("app", "config.json/app_config.json")
        ]

        for name, _ in config_files:
            self._load_config(name)

    def _load_config(self, name: str):
        """加载单个配置文件"""
        try:
            config_file = self._get_config_file_path(name)
            if not config_file.exists():
                # 创建默认配置
                default_config = self._get_default_config(name)
                if default_config:
                    self._save_config(name, default_config)
                    self.configs[name] = default_config
                return

            # 检查文件时间戳
            timestamp = config_file.stat().st_mtime
            if name in self.file_timestamps and self.file_timestamps[name] == timestamp:
                return  # 文件未变更

            # 加载配置
            with open(config_file, 'r', encoding='utf-8') as f:
                config = json.load(f)

            # 验证配置
            if name in self.schemas:
                if not self.schemas[name].validate(config):
                    print(f"配置文件 {config_file} 验证失败")
                    return

            self.configs[name] = config
            self.file_timestamps[name] = timestamp

        except Exception as e:
            print(f"加载配置文件失败 {name}: {e}")

    def _save_config(self, name: str, config: Dict[str, Any]):
        """保存配置到文件"""
        try:
            config_file = self._get_config_file_path(name)
            config_file.parent.mkdir(parents=True, exist_ok=True)

            with open(config_file, 'w', encoding='utf-8') as f:
                json.dump(config, f, indent=2, ensure_ascii=False)

            # 更新时间戳
            self.file_timestamps[name] = config_file.stat().st_mtime

        except Exception as e:
            print(f"保存配置文件失败 {name}: {e}")

    def _get_config_file_path(self, name: str) -> Path:
        """获取配置文件路径"""
        file_mapping = {
            "llm": "llm_config.json",
            "image": "config.json/image_config.json",
            "voice": "config.json/voice_config.json",
            "app": "config.json/app_config.json"
        }

        filename = file_mapping.get(name, f"{name}_config.json")
        return self.config_dir / filename

    def _get_default_config(self, name: str) -> Optional[Dict[str, Any]]:
        """获取默认配置"""
        defaults = {
            "llm": {
                "models": [
                    {
                        "name": "DefaultModel",
                        "type": "tongyi",
                        "key": "YOUR_API_KEY",
                        "url": "YOUR_API_URL"
                    }
                ]
            },
            "image": {
                "image_generation": {
                    "default_engine": "pollinations",
                    "pollinations": {"enabled": True}
                }
            },
            "voice": {
                "voice_generation": {
                    "default_engine": "edge",
                    "engines": {"edge_tts": {"enabled": True}}
                }
            },
            "app": {
                "app_settings": {
                    "version": "2.0.0",
                    "debug_mode": False
                }
            }
        }

        return defaults.get(name)

    def _apply_env_overrides(self, name: str, config: Dict[str, Any]) -> Dict[str, Any]:
        """应用环境变量覆盖"""
        config = deepcopy(config)

        # 查找相关的环境变量
        env_prefix = f"{self.env_prefix}{name.upper()}_"

        for env_key, env_value in os.environ.items():
            if env_key.startswith(env_prefix):
                # 转换环境变量键为配置键
                config_key = env_key[len(env_prefix):].lower().replace('_', '.')

                # 尝试解析值
                try:
                    # 尝试JSON解析
                    parsed_value = json.loads(env_value)
                except json.JSONDecodeError:
                    # 作为字符串处理
                    parsed_value = env_value

                # 设置配置值
                self._set_nested_value(config, config_key, parsed_value)

        return config

    def _set_nested_value(self, config: Dict[str, Any], key: str, value: Any):
        """设置嵌套配置值"""
        keys = key.split('.')
        current = config

        for k in keys[:-1]:
            if k not in current:
                current[k] = {}
            current = current[k]

        current[keys[-1]] = value

    def _notify_change_listeners(self, name: str, old_config: Optional[Dict[str, Any]],
                               new_config: Dict[str, Any]):
        """通知配置变更监听器"""
        if name in self.change_listeners:
            for listener in self.change_listeners[name]:
                try:
                    listener(name, old_config, new_config)
                except Exception as e:
                    print(f"配置变更监听器执行失败: {e}")

    def _start_hot_reload(self):
        """启动热重载监控"""
        def monitor_worker():
            while True:
                try:
                    time.sleep(1)  # 每秒检查一次

                    for name in list(self.configs.keys()):
                        config_file = self._get_config_file_path(name)
                        if config_file.exists():
                            timestamp = config_file.stat().st_mtime
                            if (name not in self.file_timestamps or
                                self.file_timestamps[name] != timestamp):
                                print(f"检测到配置文件变更: {name}")
                                old_config = self.configs.get(name)
                                self._load_config(name)
                                new_config = self.configs.get(name)

                                if old_config != new_config and new_config is not None:
                                    self._notify_change_listeners(name, old_config, new_config)

                except Exception as e:
                    print(f"热重载监控错误: {e}")

        # 在后台线程中运行监控
        monitor_thread = threading.Thread(target=monitor_worker, daemon=True)
        monitor_thread.start()


# 创建全局增强配置管理器实例
enhanced_config_manager = EnhancedConfigManager()


# 便捷函数
def get_enhanced_config(name: str) -> Optional[Dict[str, Any]]:
    """获取增强配置"""
    return enhanced_config_manager.get_config(name)


def set_enhanced_config(name: str, config: Dict[str, Any]) -> bool:
    """设置增强配置"""
    return enhanced_config_manager.set_config(name, config)


def get_enhanced_setting(config_name: str, key: str, default: Any = None) -> Any:
    """获取增强配置项"""
    return enhanced_config_manager.get_setting(config_name, key, default)


def set_enhanced_setting(config_name: str, key: str, value: Any) -> bool:
    """设置增强配置项"""
    return enhanced_config_manager.set_setting(config_name, key, value)