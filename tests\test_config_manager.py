#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
配置管理器测试
"""

import unittest
import json
import tempfile
import shutil
from pathlib import Path
from unittest.mock import patch, mock_open
from tests import BaseTestCase

from utils.config_manager import ConfigManager, EnhancedConfigManager, ConfigSchema, LogConfig


class TestConfigManager(BaseTestCase):
    """配置管理器测试类"""
    
    def setUp(self):
        """测试设置"""
        super().setUp()
        # 创建临时配置目录
        self.temp_config_dir = self.temp_dir / "config"
        self.temp_config_dir.mkdir(exist_ok=True)
        
        # 创建测试配置文件
        self.test_llm_config = {
            "models": [
                {
                    "name": "TestModel",
                    "type": "openai",
                    "key": "test_key",
                    "url": "test_url"
                }
            ]
        }
        
        llm_config_file = self.temp_config_dir / "llm_config.json"
        with open(llm_config_file, 'w', encoding='utf-8') as f:
            json.dump(self.test_llm_config, f)
    
    def tearDown(self):
        """测试清理"""
        super().tearDown()
        # 清理临时文件
        if self.temp_config_dir.exists():
            shutil.rmtree(self.temp_config_dir)
    
    def test_config_manager_initialization(self):
        """测试配置管理器初始化"""
        config_manager = ConfigManager(str(self.temp_config_dir))
        
        self.assertEqual(config_manager.config_dir, str(self.temp_config_dir))
        self.assertIsInstance(config_manager.config, dict)
        self.assertIn("models", config_manager.config)
    
    def test_load_config(self):
        """测试配置加载"""
        config_manager = ConfigManager(str(self.temp_config_dir))
        
        # 检查是否正确加载了测试配置
        models = config_manager.get_models()
        self.assertEqual(len(models), 1)
        self.assertEqual(models[0]["name"], "TestModel")
    
    def test_get_model_config(self):
        """测试获取模型配置"""
        config_manager = ConfigManager(str(self.temp_config_dir))
        
        model_config = config_manager.get_model_config("TestModel")
        self.assertIsNotNone(model_config)
        self.assertEqual(model_config["type"], "openai")
        self.assertEqual(model_config["key"], "test_key")
        
        # 测试不存在的模型
        non_existent = config_manager.get_model_config("NonExistent")
        self.assertIsNone(non_existent)
    
    def test_save_model_config(self):
        """测试保存模型配置"""
        config_manager = ConfigManager(str(self.temp_config_dir))
        
        # 保存新模型配置
        config_manager.save_model_config(
            "NewModel",
            "gpt",
            "new_key",
            "new_url"
        )
        
        # 验证保存
        new_model = config_manager.get_model_config("NewModel")
        self.assertIsNotNone(new_model)
        self.assertEqual(new_model["type"], "gpt")
        self.assertEqual(new_model["key"], "new_key")
        self.assertEqual(new_model["url"], "new_url")
    
    def test_model_operations(self):
        """测试模型操作"""
        config_manager = ConfigManager(str(self.temp_config_dir))
        
        # 添加模型
        new_model = {
            "name": "AddedModel",
            "type": "claude",
            "key": "added_key",
            "url": "added_url"
        }
        config_manager.add_model(new_model)
        
        added_model = config_manager.get_model_by_name("AddedModel")
        self.assertIsNotNone(added_model)
        self.assertEqual(added_model["type"], "claude")
        
        # 更新模型
        updated_model = {
            "name": "AddedModel",
            "type": "updated_claude",
            "key": "updated_key",
            "url": "updated_url"
        }
        config_manager.update_model("AddedModel", updated_model)
        
        updated = config_manager.get_model_by_name("AddedModel")
        self.assertEqual(updated["type"], "updated_claude")
        
        # 删除模型
        config_manager.remove_model("AddedModel")
        removed = config_manager.get_model_by_name("AddedModel")
        self.assertIsNone(removed)
    
    def test_app_settings(self):
        """测试应用设置"""
        config_manager = ConfigManager(str(self.temp_config_dir))
        
        # 保存应用设置
        app_settings = {
            "theme": "dark",
            "language": "zh-CN",
            "auto_save": True
        }
        
        result = config_manager.save_app_settings(app_settings)
        self.assertTrue(result)
        
        # 验证设置文件是否创建
        settings_file = Path(self.temp_config_dir) / "app_settings.json"
        self.assertTrue(settings_file.exists())
        
        # 读取并验证内容
        with open(settings_file, 'r', encoding='utf-8') as f:
            saved_settings = json.load(f)
        
        self.assertEqual(saved_settings["theme"], "dark")
        self.assertEqual(saved_settings["language"], "zh-CN")
        self.assertTrue(saved_settings["auto_save"])


class TestEnhancedConfigManager(BaseTestCase):
    """增强配置管理器测试类"""
    
    def setUp(self):
        """测试设置"""
        super().setUp()
        self.temp_config_dir = self.temp_dir / "enhanced_config"
        self.enhanced_config_manager = EnhancedConfigManager(str(self.temp_config_dir))
    
    def test_enhanced_config_manager_initialization(self):
        """测试增强配置管理器初始化"""
        self.assertEqual(self.enhanced_config_manager.config_dir, Path(self.temp_config_dir))
        self.assertIsInstance(self.enhanced_config_manager.configs, dict)
        self.assertIsInstance(self.enhanced_config_manager.schemas, dict)
    
    def test_schema_registration(self):
        """测试配置模式注册"""
        schema = ConfigSchema(
            name="test_schema",
            required_fields=["field1", "field2"],
            validators={
                "field1": lambda x: isinstance(x, str),
                "field2": lambda x: isinstance(x, int) and x > 0
            }
        )
        
        self.enhanced_config_manager.register_schema(schema)
        self.assertIn("test_schema", self.enhanced_config_manager.schemas)
    
    def test_config_validation(self):
        """测试配置验证"""
        schema = ConfigSchema(
            name="validation_test",
            required_fields=["required_field"],
            validators={
                "required_field": lambda x: isinstance(x, str) and len(x) > 0
            }
        )
        
        # 有效配置
        valid_config = {"required_field": "valid_value"}
        self.assertTrue(schema.validate(valid_config))
        
        # 缺少必需字段
        invalid_config1 = {}
        self.assertFalse(schema.validate(invalid_config1))
        
        # 验证器失败
        invalid_config2 = {"required_field": ""}
        self.assertFalse(schema.validate(invalid_config2))
    
    def test_config_operations(self):
        """测试配置操作"""
        config_name = "test_config"
        test_config = {
            "setting1": "value1",
            "setting2": {
                "nested": "nested_value"
            }
        }
        
        # 设置配置
        result = self.enhanced_config_manager.set_config(config_name, test_config)
        self.assertTrue(result)
        
        # 获取配置
        retrieved_config = self.enhanced_config_manager.get_config(config_name)
        self.assertIsNotNone(retrieved_config)
        self.assertEqual(retrieved_config["setting1"], "value1")
        self.assertEqual(retrieved_config["setting2"]["nested"], "nested_value")
    
    def test_setting_operations(self):
        """测试配置项操作"""
        config_name = "setting_test"
        
        # 设置嵌套配置项
        self.enhanced_config_manager.set_setting(config_name, "level1.level2.value", "test_value")
        
        # 获取配置项
        value = self.enhanced_config_manager.get_setting(config_name, "level1.level2.value")
        self.assertEqual(value, "test_value")
        
        # 获取不存在的配置项
        non_existent = self.enhanced_config_manager.get_setting(config_name, "non.existent", "default")
        self.assertEqual(non_existent, "default")
    
    def test_change_listeners(self):
        """测试配置变更监听器"""
        config_name = "listener_test"
        change_events = []
        
        def change_listener(name, old_config, new_config):
            change_events.append((name, old_config, new_config))
        
        # 添加监听器
        self.enhanced_config_manager.add_change_listener(config_name, change_listener)
        
        # 设置配置
        test_config = {"test": "value"}
        self.enhanced_config_manager.set_config(config_name, test_config)
        
        # 验证监听器被调用
        self.assertEqual(len(change_events), 1)
        self.assertEqual(change_events[0][0], config_name)
        self.assertEqual(change_events[0][2], test_config)
        
        # 移除监听器
        self.enhanced_config_manager.remove_change_listener(config_name, change_listener)
        
        # 再次设置配置
        self.enhanced_config_manager.set_config(config_name, {"test": "new_value"})
        
        # 验证监听器没有被调用
        self.assertEqual(len(change_events), 1)
    
    @patch.dict('os.environ', {'A2_TEST_CONFIG_SETTING': 'env_value'})
    def test_environment_variable_override(self):
        """测试环境变量覆盖"""
        config_name = "test"
        test_config = {"config": {"setting": "original_value"}}
        
        self.enhanced_config_manager.set_config(config_name, test_config, save=False)
        
        # 获取配置（应该应用环境变量覆盖）
        config = self.enhanced_config_manager.get_config(config_name)
        
        # 注意：环境变量覆盖的具体实现可能需要调整
        # 这里只是测试框架
        self.assertIsNotNone(config)


class TestConfigSchema(BaseTestCase):
    """配置模式测试类"""
    
    def test_schema_creation(self):
        """测试配置模式创建"""
        schema = ConfigSchema(
            name="test_schema",
            required_fields=["field1"],
            optional_fields={"field2": "default_value"},
            validators={"field1": lambda x: x is not None}
        )
        
        self.assertEqual(schema.name, "test_schema")
        self.assertEqual(schema.required_fields, ["field1"])
        self.assertEqual(schema.optional_fields, {"field2": "default_value"})
        self.assertIn("field1", schema.validators)
    
    def test_schema_validation_success(self):
        """测试配置模式验证成功"""
        schema = ConfigSchema(
            name="test",
            required_fields=["name", "value"],
            validators={
                "name": lambda x: isinstance(x, str),
                "value": lambda x: isinstance(x, int) and x > 0
            }
        )
        
        valid_config = {
            "name": "test_name",
            "value": 42
        }
        
        self.assertTrue(schema.validate(valid_config))
    
    def test_schema_validation_failure(self):
        """测试配置模式验证失败"""
        schema = ConfigSchema(
            name="test",
            required_fields=["name"],
            validators={
                "name": lambda x: isinstance(x, str) and len(x) > 0
            }
        )
        
        # 缺少必需字段
        invalid_config1 = {}
        self.assertFalse(schema.validate(invalid_config1))
        
        # 验证器失败
        invalid_config2 = {"name": ""}
        self.assertFalse(schema.validate(invalid_config2))
        
        # 验证器异常
        invalid_config3 = {"name": None}
        self.assertFalse(schema.validate(invalid_config3))


if __name__ == '__main__':
    unittest.main()
