#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试代理节点对Google API的可用性
"""

import requests
import json
import winreg

def test_proxy_for_google():
    """测试当前代理对Google API的可用性"""
    print("=" * 60)
    print("代理节点Google API可用性测试")
    print("=" * 60)
    
    # 获取系统代理设置
    try:
        with winreg.OpenKey(winreg.HKEY_CURRENT_USER, 
                           r"Software\Microsoft\Windows\CurrentVersion\Internet Settings") as key:
            proxy_enable = winreg.QueryValueEx(key, "ProxyEnable")[0]
            if proxy_enable:
                proxy_server = winreg.QueryValueEx(key, "ProxyServer")[0]
                if not proxy_server.startswith('http'):
                    proxy_url = f"http://{proxy_server}"
                else:
                    proxy_url = proxy_server
                print(f"🔍 检测到系统代理: {proxy_url}")
            else:
                print("❌ 系统代理未启用")
                return
    except Exception as e:
        print(f"❌ 读取代理设置失败: {e}")
        return
    
    # 测试基本连接
    print(f"\n🧪 测试1: 基本代理连接")
    try:
        response = requests.get(
            "https://httpbin.org/ip",
            proxies={"http": proxy_url, "https": proxy_url},
            timeout=10
        )
        if response.status_code == 200:
            ip_info = response.json()
            print(f"✅ 代理连接正常，当前IP: {ip_info.get('origin', 'unknown')}")
        else:
            print(f"❌ 代理连接失败，状态码: {response.status_code}")
            return
    except Exception as e:
        print(f"❌ 代理连接测试失败: {e}")
        return
    
    # 测试Google连接
    print(f"\n🧪 测试2: Google网站连接")
    try:
        response = requests.get(
            "https://www.google.com",
            proxies={"http": proxy_url, "https": proxy_url},
            timeout=15
        )
        if response.status_code == 200:
            print(f"✅ 可以访问Google网站")
        else:
            print(f"❌ Google网站访问失败，状态码: {response.status_code}")
    except Exception as e:
        print(f"❌ Google网站访问失败: {e}")
    
    # 测试Google API
    print(f"\n🧪 测试3: Google Gemini API")
    
    # 从配置文件读取API密钥
    try:
        with open('config/llm_config.json', 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        google_config = None
        for model in config['models']:
            if model['type'] == 'google':
                google_config = model
                break
        
        if not google_config:
            print("❌ 未找到Google API配置")
            return
        
        api_key = google_config['key']
        api_url = google_config['url']
        
        # 构造API请求
        url = f"{api_url}?key={api_key}"
        data = {
            "contents": [{
                "parts": [{"text": "Hello, please reply with just 'API test successful'"}]
            }],
            "generationConfig": {
                "maxOutputTokens": 20,
                "temperature": 0.7
            }
        }
        
        headers = {
            'Content-Type': 'application/json',
            'User-Agent': 'AI-Video-Generator/1.0'
        }
        
        response = requests.post(
            url,
            json=data,
            headers=headers,
            proxies={"http": proxy_url, "https": proxy_url},
            timeout=30
        )
        
        if response.status_code == 200:
            result = response.json()
            content = result['candidates'][0]['content']['parts'][0]['text']
            print(f"✅ Google API调用成功: {content}")
        elif response.status_code == 502:
            print(f"❌ Google API返回502错误 - 代理节点可能有问题")
            print(f"   建议: 在Hiddify中切换到其他节点")
        elif response.status_code == 403:
            print(f"❌ Google API返回403错误 - 可能是地区限制")
            print(f"   建议: 切换到美国或新加坡节点")
        else:
            print(f"❌ Google API调用失败，状态码: {response.status_code}")
            print(f"   响应内容: {response.text[:200]}...")
            
    except Exception as e:
        print(f"❌ Google API测试失败: {e}")
    
    print(f"\n📋 建议:")
    print(f"• 如果看到502错误，请在Hiddify中切换代理节点")
    print(f"• 推荐使用美国、新加坡、香港等节点")
    print(f"• 避免使用可能被Google限制的节点")
    print(f"• 切换节点后重新测试")

if __name__ == "__main__":
    test_proxy_for_google()
