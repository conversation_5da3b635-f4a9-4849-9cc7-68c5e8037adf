#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
在程序环境中测试角色提取功能
"""

import os
import sys
import time

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_character_extraction():
    """测试角色提取功能"""
    print("=" * 60)
    print("程序环境角色提取测试")
    print("=" * 60)
    
    try:
        # 初始化服务管理器（模拟程序启动）
        print("🔧 初始化服务管理器...")
        from src.core.service_manager import ServiceManager
        from src.utils.config_manager import ConfigManager
        
        config_manager = ConfigManager("config")
        service_manager = ServiceManager(config_manager)
        
        # 异步初始化
        import asyncio
        async def init_services():
            await service_manager.initialize()
        
        asyncio.run(init_services())
        print("✅ 服务管理器初始化完成")
        
        # 初始化角色场景管理器
        print("🔧 初始化角色场景管理器...")
        from src.utils.character_scene_manager import CharacterSceneManager
        
        # 模拟项目数据
        project_data = {
            'project_name': '测试项目',
            'project_root': 'output/test_project'
        }
        
        char_scene_manager = CharacterSceneManager(
            project_data=project_data,
            service_manager=service_manager
        )
        print("✅ 角色场景管理器初始化完成")
        
        # 测试角色提取
        print("\n🧪 测试角色提取功能...")
        
        # 使用简单的测试文本
        test_text = """
        三顾茅庐的故事：
        
        刘备是蜀汉的开国皇帝，为人仁德，求贤若渴。他听说诸葛亮是一位才华横溢的智者，居住在隆中。
        
        诸葛亮字孔明，号卧龙，是一位年轻的谋士，博学多才，精通兵法。
        
        关羽和张飞是刘备的结义兄弟，关羽红脸长髯，张飞黑脸虬髯，都是勇猛的武将。
        """
        
        world_bible = """
        故事背景：三国时期，天下三分，群雄争霸。
        主要人物：刘备（仁德君主）、诸葛亮（智慧谋士）、关羽（忠义武将）、张飞（勇猛武将）
        故事主题：求贤若渴，三顾茅庐的典故
        """
        
        print(f"📝 测试文本长度: {len(test_text)} 字符")
        print(f"📝 世界观长度: {len(world_bible)} 字符")
        
        start_time = time.time()
        
        try:
            # 调用角色提取
            characters = char_scene_manager.extract_characters_from_text(
                text=test_text,
                world_bible=world_bible
            )
            
            end_time = time.time()
            duration = end_time - start_time
            
            print(f"⏱️ 角色提取耗时: {duration:.2f}秒")
            
            if characters:
                print(f"✅ 角色提取成功，共提取 {len(characters)} 个角色:")
                for i, char in enumerate(characters, 1):
                    print(f"   {i}. {char.get('name', '未知')} - {char.get('description', '无描述')[:50]}...")
            else:
                print("❌ 角色提取失败，未返回任何角色")
                
        except Exception as e:
            end_time = time.time()
            duration = end_time - start_time
            print(f"❌ 角色提取异常 ({duration:.2f}秒): {e}")
            import traceback
            print(f"详细错误: {traceback.format_exc()}")
        
        # 测试场景提取
        print(f"\n🧪 测试场景提取功能...")
        
        start_time = time.time()
        
        try:
            # 调用场景提取
            scenes = char_scene_manager.extract_scenes_from_text(
                text=test_text,
                world_bible=world_bible
            )
            
            end_time = time.time()
            duration = end_time - start_time
            
            print(f"⏱️ 场景提取耗时: {duration:.2f}秒")
            
            if scenes:
                print(f"✅ 场景提取成功，共提取 {len(scenes)} 个场景:")
                for i, scene in enumerate(scenes, 1):
                    print(f"   {i}. {scene.get('name', '未知')} - {scene.get('description', '无描述')[:50]}...")
            else:
                print("❌ 场景提取失败，未返回任何场景")
                
        except Exception as e:
            end_time = time.time()
            duration = end_time - start_time
            print(f"❌ 场景提取异常 ({duration:.2f}秒): {e}")
            import traceback
            print(f"详细错误: {traceback.format_exc()}")
        
        # 检查服务状态
        print(f"\n📊 服务状态检查:")
        from src.core.service_manager import ServiceType
        llm_service = service_manager.get_service(ServiceType.LLM)
        if llm_service:
            status = llm_service.get_status()
            print(f"   LLM服务状态: {status['status']}")
            print(f"   请求次数: {status['request_count']}")
            print(f"   成功次数: {status['success_count']}")
            print(f"   成功率: {status['success_rate']:.1%}")
            if status['last_error']:
                print(f"   最后错误: {status['last_error']}")
        
        print(f"\n📋 总结:")
        print(f"• 增加了线程池超时时间到180秒")
        print(f"• 添加了详细的日志记录")
        print(f"• 优化了事件循环清理")
        print(f"• 使用故障转移机制")
        
    except Exception as e:
        print(f"❌ 测试过程中发生异常: {e}")
        import traceback
        print(f"详细错误: {traceback.format_exc()}")

if __name__ == "__main__":
    test_character_extraction()
