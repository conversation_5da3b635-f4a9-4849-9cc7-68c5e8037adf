#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试旧LLMApi类的超时修复
"""

import os
import sys
import json

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from src.models.llm_api import LLMApi

def test_old_llm_api_timeout():
    """测试旧LLMApi类的超时设置"""
    print("=" * 60)
    print("旧LLMApi类超时修复测试")
    print("=" * 60)
    
    try:
        # 从配置文件读取API配置
        with open('config/llm_config.json', 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        # 找到智谱AI配置
        zhipu_config = None
        for model in config['models']:
            if model['type'] == 'zhipu':
                zhipu_config = model
                break
        
        if not zhipu_config:
            print("❌ 未找到智谱AI配置")
            return
        
        print(f"📊 使用配置:")
        print(f"   名称: {zhipu_config['name']}")
        print(f"   类型: {zhipu_config['type']}")
        print(f"   URL: {zhipu_config['url']}")
        print(f"   超时: {zhipu_config.get('timeout', '未设置')}秒")
        
        # 初始化LLMApi（模拟五阶段分镜系统的调用）
        print(f"\n🔧 初始化LLMApi...")
        llm_api = LLMApi(
            api_type=zhipu_config['type'],
            api_key=zhipu_config['key'],
            api_url=zhipu_config['url']
        )
        
        print(f"✅ LLMApi初始化成功")
        print(f"   API类型: {llm_api.api_type}")
        print(f"   分镜模型: {llm_api.shots_model_name}")
        
        # 构造角色提取的长提示词
        character_prompt = """
        请分析以下文本，提取出所有角色信息，包括姓名、外貌描述、性格特点、身份地位等详细信息。
        
        文本内容：
        三国演义的故事背景设定在东汉末年，天下大乱，群雄并起的时代。

        主要角色包括：
        
        刘备：字玄德，蜀汉开国皇帝，仁德之君，身长七尺五寸，两耳垂肩，双手过膝，面如冠玉，唇若涂脂。性格仁慈宽厚，礼贤下士，有帝王之相。出身微寒，早年以织席贩履为业，后因黄巾起义而投军，逐渐崭露头角。他重视人才，善于团结人心，以仁德著称于世。在桃园三结义中与关羽、张飞结为异姓兄弟，共同创建蜀汉政权。
        
        关羽：字云长，刘备义弟，蜀汉五虎上将之首，身长九尺，髯长二尺，面如重枣，唇若涂脂，丹凤眼，卧蚕眉，相貌堂堂，威风凛凛。性格忠义无双，武艺高强，有万夫不当之勇。他忠心耿耿，义薄云天，被后世尊为"武圣"。关羽骄傲自负，看不起士大夫，但对刘备忠心不二，宁死不降。
        
        张飞：字翼德，刘备义弟，蜀汉五虎上将，身长八尺，豹头环眼，燕颔虎须，声若巨雷，势如奔马。性格粗犷豪爽，勇猛无敌，但有时鲁莽冲动。他出身富商，家境殷实，在桃园三结义中慷慨资助刘备起兵。张飞虽然外表粗犷，但内心细腻，善于识人，对有才能的人十分敬重。
        
        诸葛亮：字孔明，号卧龙，蜀汉丞相，身长八尺，面如冠玉，头戴纶巾，身披鹤氅，飘飘然有神仙之概。智谋过人，忠心耿耿，被誉为"智圣"。他出身名门，博学多才，精通天文地理、奇门遁甲、兵法战策。诸葛亮为人谨慎，做事周密，对蜀汉政权忠心不二，鞠躬尽瘁，死而后已。
        
        请按照以下格式输出角色信息：
        1. 角色姓名
        2. 字号别名
        3. 外貌描述
        4. 性格特点
        5. 身份地位
        6. 主要事迹
        
        要求详细分析每个角色的特点，不要遗漏任何重要信息。
        """
        
        print(f"\n🧪 测试角色提取调用（180秒超时）...")
        print(f"   提示词长度: {len(character_prompt)} 字符")
        
        import time
        start_time = time.time()
        
        # 构造消息格式
        messages = [
            {"role": "system", "content": "你是一个专业的角色分析师，擅长从故事中提取角色信息。"},
            {"role": "user", "content": character_prompt}
        ]
        
        # 调用API（使用包含"角色"的任务名称来触发180秒超时）
        result = llm_api._make_api_call(
            llm_api.shots_model_name,
            messages,
            "角色提取测试"  # 任务名称包含"角色"，会触发180秒超时
        )
        
        end_time = time.time()
        duration = end_time - start_time
        
        print(f"\n📊 测试结果:")
        print(f"   耗时: {duration:.2f}秒")
        
        if isinstance(result, str) and not result.startswith("API请求失败"):
            print(f"✅ 角色提取成功！")
            print(f"   返回内容长度: {len(result)} 字符")
            print(f"   内容预览: {result[:200]}...")
        else:
            print(f"❌ 角色提取失败: {result}")
        
        print(f"\n📋 修复总结:")
        print(f"• 角色提取任务超时时间: 180秒")
        print(f"• 其他任务超时时间: 120秒")
        print(f"• 如果测试成功，说明旧LLMApi的超时问题已解决")
        print(f"• 这将修复五阶段分镜系统中的角色提取超时问题")
        
    except Exception as e:
        print(f"❌ 测试过程中发生异常: {e}")
        import traceback
        print(f"详细错误: {traceback.format_exc()}")

if __name__ == "__main__":
    test_old_llm_api_timeout()
