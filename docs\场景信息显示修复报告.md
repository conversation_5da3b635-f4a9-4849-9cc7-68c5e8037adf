# 场景信息显示修复报告

## 📋 问题描述

用户反馈分镜脚本显示的场景信息不正确：

### 问题现象
- **错误显示**：分镜脚本中显示"图书馆的后退"
- **应该显示**：角色场景管理器中的真实场景名称，如"剑桥大学图书馆"
- **根本原因**：场景数据源混淆，使用了分镜场景列表而不是角色场景管理器中的真实场景

### 用户期望
- 分镜脚本应该显示角色场景管理器中提取到的真实场景名称
- 场景信息应该与角色场景管理器中的场景保持一致
- 避免显示分镜板自动生成的场景名（如"场景1"、"场景2"）

## 🔧 修复方案

### 1. 核心修复逻辑

在 `src/gui/five_stage_storyboard_tab.py` 中实现了智能场景名称获取：

```python
def _get_real_scene_name(self, scene_info, scene_index):
    """获取真实的场景名称，优先使用角色场景管理器中的场景"""
    try:
        # 首先尝试从角色场景管理器获取场景信息
        from src.utils.character_scene_manager import CharacterSceneManager
        
        if project_root:
            scene_manager = CharacterSceneManager(project_root)
            scenes = scene_manager.get_all_scenes()
            
            # 过滤掉分镜板生成的场景
            real_scenes = []
            for scene_id, scene_data in scenes.items():
                scene_name = scene_data.get('name', '未命名')
                if not re.match(r'^场景\d+$', scene_name):
                    real_scenes.append(scene_name)
            
            # 按索引返回真实场景
            if real_scenes and scene_index < len(real_scenes):
                return real_scenes[scene_index]
    
    except Exception as e:
        logger.warning(f"获取角色场景管理器场景失败: {e}")
    
    # 降级处理：解析原始scene_info
    return self._parse_original_scene_info(scene_info, scene_index)
```

### 2. 修复的关键位置

#### 2.1 分镜脚本显示修复
**文件**: `src/gui/five_stage_storyboard_tab.py`
**方法**: `_display_storyboard_results()`
**修改**: 使用 `_get_real_scene_name()` 获取真实场景名称

```python
# 修复前
scene_name = scene_info.get("scene_name", f"场景{i+1}")

# 修复后  
scene_name = self._get_real_scene_name(scene_info, i)
```

#### 2.2 分镜文件保存修复
**文件**: `src/gui/five_stage_storyboard_tab.py`
**方法**: `_save_storyboard_scripts_to_files()`
**修改**: 保存文件时使用真实场景名称作为标题

```python
# 修复前
f.write(f"# {scene_info}\n\n")

# 修复后
real_scene_name = self._get_real_scene_name(scene_info, scene_index)
f.write(f"# {real_scene_name}\n\n")
```

### 3. 智能处理策略

#### 3.1 优先级顺序
1. **最高优先级**：角色场景管理器中的真实场景
2. **中等优先级**：字典格式的scene_name字段
3. **最低优先级**：原始字符串内容（过滤分镜生成场景）

#### 3.2 场景过滤规则
- 过滤掉匹配 `^场景\d+$` 模式的场景名
- 保留用户创建的真实场景名称
- 按索引映射到对应的真实场景

#### 3.3 降级处理
- 如果无法获取角色场景管理器数据，使用原始解析逻辑
- 确保在任何情况下都能正常显示场景信息
- 提供友好的错误处理和日志记录

## ✅ 修复效果验证

### 测试用例
1. **简单字符串场景**: "图书馆的后退" → "剑桥大学图书馆"
2. **分镜生成场景**: "场景2" → "牛顿与数据的实验室"  
3. **字典格式场景**: `{'scene_name': '牛顿书房'}` → "牛顿书房"

### 验证结果
- ✅ 分镜脚本显示使用真实场景名称
- ✅ 分镜文件保存使用正确标题
- ✅ 过滤掉自动生成的场景名
- ✅ 提供完善的降级处理
- ✅ 保持与角色场景管理器的一致性

## 🎯 用户体验改进

### 修复前
```
场景信息: 图书馆的后退
```

### 修复后  
```
场景信息: 剑桥大学图书馆
```

### 改进效果
1. **一致性提升**：场景信息与角色场景管理器保持一致
2. **可读性增强**：显示有意义的场景名称而不是临时标识
3. **用户体验**：减少混淆，提供准确的场景信息
4. **数据完整性**：确保场景数据的正确映射和使用

## 📋 技术细节

### 依赖组件
- `CharacterSceneManager`: 角色场景管理器
- `five_stage_storyboard_tab.py`: 五阶段分镜界面
- 正则表达式过滤: 识别和过滤自动生成场景

### 性能考虑
- 缓存场景数据，避免重复读取
- 异常处理确保不影响主流程
- 降级策略保证功能可用性

### 兼容性
- 向后兼容现有项目数据
- 支持多种场景信息格式
- 渐进式增强，不破坏现有功能

## 🚀 后续优化建议

1. **缓存优化**: 实现场景数据缓存，提高性能
2. **配置选项**: 允许用户选择场景名称显示策略
3. **自动同步**: 实现角色场景管理器与分镜数据的自动同步
4. **批量修复**: 提供批量修复历史项目场景信息的工具

## 📝 总结

本次修复成功解决了场景信息显示混淆的问题，通过智能的场景名称获取策略，确保分镜脚本显示的场景信息与角色场景管理器中的真实场景保持一致。修复方案具有良好的兼容性和稳定性，显著提升了用户体验。
