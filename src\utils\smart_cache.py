#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智能缓存管理器

提供多层级缓存策略，包括：
- 内存缓存（LRU）
- 磁盘缓存
- 分布式缓存（可选）
- 智能预加载
- 缓存预热
- 自动清理
"""

import os
import time
import json
import hashlib
import threading
import asyncio
from typing import Any, Dict, List, Optional, Callable, Union
from dataclasses import dataclass
from pathlib import Path
from collections import OrderedDict, defaultdict

from utils.logger import logger


@dataclass
class CacheConfig:
    """缓存配置"""
    max_memory_size: int = 100 * 1024 * 1024  # 100MB
    max_disk_size: int = 1024 * 1024 * 1024   # 1GB
    max_items: int = 10000
    ttl: int = 3600  # 1小时
    cleanup_interval: int = 300  # 5分钟
    preload_enabled: bool = True
    compression_enabled: bool = True


@dataclass
class CacheItem:
    """缓存项"""
    key: str
    data: Any
    size: int
    created_time: float
    last_access_time: float
    access_count: int = 0
    ttl: Optional[int] = None
    
    def is_expired(self) -> bool:
        """检查是否过期"""
        if self.ttl is None:
            return False
        return time.time() - self.created_time > self.ttl
    
    def update_access(self):
        """更新访问信息"""
        self.last_access_time = time.time()
        self.access_count += 1


class SmartCache:
    """
    智能缓存管理器
    
    特性：
    - 多层级缓存（内存 + 磁盘）
    - LRU + TTL 过期策略
    - 智能预加载
    - 压缩存储
    - 异步操作
    - 性能监控
    """
    
    def __init__(self, name: str = "default", config: Optional[CacheConfig] = None):
        self.name = name
        self.config = config or CacheConfig()
        
        # 内存缓存
        self.memory_cache: OrderedDict[str, CacheItem] = OrderedDict()
        self.memory_size = 0
        
        # 磁盘缓存
        self.cache_dir = Path(f"temp/cache/{name}")
        self.cache_dir.mkdir(parents=True, exist_ok=True)
        self.disk_index_file = self.cache_dir / "index.json"
        self.disk_index = self._load_disk_index()
        
        # 线程安全
        self.lock = threading.RLock()
        
        # 统计信息
        self.stats = {
            'hits': 0,
            'misses': 0,
            'evictions': 0,
            'disk_reads': 0,
            'disk_writes': 0,
            'preloads': 0
        }
        
        # 预加载队列
        self.preload_queue = asyncio.Queue() if asyncio.get_event_loop().is_running() else None
        self.preload_patterns = defaultdict(list)
        
        # 启动清理任务
        self._start_cleanup_task()
    
    def get(self, key: str, default: Any = None) -> Any:
        """
        获取缓存项
        
        Args:
            key: 缓存键
            default: 默认值
            
        Returns:
            Any: 缓存的数据或默认值
        """
        with self.lock:
            # 检查内存缓存
            if key in self.memory_cache:
                item = self.memory_cache[key]
                if not item.is_expired():
                    # 移动到末尾（LRU）
                    self.memory_cache.move_to_end(key)
                    item.update_access()
                    self.stats['hits'] += 1
                    
                    # 记录访问模式用于预加载
                    self._record_access_pattern(key)
                    
                    return item.data
                else:
                    # 过期，删除
                    self._remove_from_memory(key)
            
            # 检查磁盘缓存
            disk_data = self._load_from_disk(key)
            if disk_data is not None:
                # 加载到内存
                self._put_to_memory(key, disk_data, ttl=self.config.ttl)
                self.stats['hits'] += 1
                self.stats['disk_reads'] += 1
                return disk_data
            
            self.stats['misses'] += 1
            return default
    
    def put(self, key: str, data: Any, ttl: Optional[int] = None) -> bool:
        """
        存储缓存项
        
        Args:
            key: 缓存键
            data: 要缓存的数据
            ttl: 生存时间（秒），None表示使用默认TTL
            
        Returns:
            bool: 是否成功存储
        """
        try:
            with self.lock:
                # 存储到内存
                success = self._put_to_memory(key, data, ttl)
                
                # 异步存储到磁盘
                if success:
                    self._save_to_disk_async(key, data, ttl)
                
                return success
                
        except Exception as e:
            logger.error(f"缓存存储失败 {key}: {e}")
            return False
    
    def remove(self, key: str) -> bool:
        """删除缓存项"""
        with self.lock:
            removed = False
            
            # 从内存删除
            if key in self.memory_cache:
                self._remove_from_memory(key)
                removed = True
            
            # 从磁盘删除
            if key in self.disk_index:
                self._remove_from_disk(key)
                removed = True
            
            return removed
    
    def clear(self):
        """清空所有缓存"""
        with self.lock:
            self.memory_cache.clear()
            self.memory_size = 0
            
            # 清空磁盘缓存
            for cache_file in self.cache_dir.glob("*.cache"):
                cache_file.unlink(missing_ok=True)
            
            self.disk_index.clear()
            self._save_disk_index()
    
    def _put_to_memory(self, key: str, data: Any, ttl: Optional[int] = None) -> bool:
        """存储到内存缓存"""
        try:
            data_size = self._estimate_size(data)
            
            # 检查是否需要清理空间
            while (self.memory_size + data_size > self.config.max_memory_size or 
                   len(self.memory_cache) >= self.config.max_items):
                if not self.memory_cache:
                    break
                self._evict_lru()
            
            # 如果数据太大，不缓存到内存
            if data_size > self.config.max_memory_size // 2:
                return False
            
            # 创建缓存项
            current_time = time.time()
            item = CacheItem(
                key=key,
                data=data,
                size=data_size,
                created_time=current_time,
                last_access_time=current_time,
                ttl=ttl or self.config.ttl
            )
            
            # 如果已存在，先删除
            if key in self.memory_cache:
                self._remove_from_memory(key)
            
            # 添加新项
            self.memory_cache[key] = item
            self.memory_size += data_size
            
            return True
            
        except Exception as e:
            logger.error(f"内存缓存存储失败 {key}: {e}")
            return False
    
    def _remove_from_memory(self, key: str):
        """从内存缓存删除"""
        if key in self.memory_cache:
            item = self.memory_cache.pop(key)
            self.memory_size -= item.size
    
    def _evict_lru(self):
        """清理最少使用的缓存项"""
        if self.memory_cache:
            key, item = self.memory_cache.popitem(last=False)
            self.memory_size -= item.size
            self.stats['evictions'] += 1
    
    def _estimate_size(self, data: Any) -> int:
        """估算数据大小"""
        try:
            import pickle
            return len(pickle.dumps(data))
        except:
            return 1024  # 默认1KB
    
    def _load_disk_index(self) -> Dict:
        """加载磁盘缓存索引"""
        try:
            if self.disk_index_file.exists():
                with open(self.disk_index_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
        except Exception as e:
            logger.warning(f"加载磁盘缓存索引失败: {e}")
        return {}
    
    def _save_disk_index(self):
        """保存磁盘缓存索引"""
        try:
            with open(self.disk_index_file, 'w', encoding='utf-8') as f:
                json.dump(self.disk_index, f, indent=2, ensure_ascii=False)
        except Exception as e:
            logger.warning(f"保存磁盘缓存索引失败: {e}")
    
    def _load_from_disk(self, key: str) -> Any:
        """从磁盘加载缓存"""
        try:
            if key not in self.disk_index:
                return None
            
            cache_file = self.cache_dir / f"{key}.cache"
            if not cache_file.exists():
                # 清理无效索引
                del self.disk_index[key]
                self._save_disk_index()
                return None
            
            # 检查是否过期
            item_info = self.disk_index[key]
            if item_info.get('ttl') and time.time() - item_info['created'] > item_info['ttl']:
                self._remove_from_disk(key)
                return None
            
            # 加载数据
            import pickle
            with open(cache_file, 'rb') as f:
                data = pickle.load(f)
            
            return data
            
        except Exception as e:
            logger.warning(f"从磁盘加载缓存失败 {key}: {e}")
            return None
    
    def _save_to_disk_async(self, key: str, data: Any, ttl: Optional[int] = None):
        """异步保存到磁盘"""
        def save_worker():
            try:
                import pickle
                cache_file = self.cache_dir / f"{key}.cache"
                
                with open(cache_file, 'wb') as f:
                    pickle.dump(data, f)
                
                # 更新索引
                self.disk_index[key] = {
                    'file': str(cache_file),
                    'created': time.time(),
                    'size': cache_file.stat().st_size,
                    'ttl': ttl
                }
                self._save_disk_index()
                self.stats['disk_writes'] += 1
                
            except Exception as e:
                logger.warning(f"磁盘缓存保存失败 {key}: {e}")
        
        # 在后台线程中保存
        threading.Thread(target=save_worker, daemon=True).start()
    
    def _remove_from_disk(self, key: str):
        """从磁盘删除缓存"""
        try:
            if key in self.disk_index:
                cache_file = self.cache_dir / f"{key}.cache"
                cache_file.unlink(missing_ok=True)
                del self.disk_index[key]
                self._save_disk_index()
        except Exception as e:
            logger.warning(f"磁盘缓存删除失败 {key}: {e}")
    
    def _record_access_pattern(self, key: str):
        """记录访问模式用于预加载"""
        if not self.config.preload_enabled:
            return
        
        # 简单的模式识别：记录最近访问的键
        current_time = time.time()
        pattern_key = f"recent_{int(current_time // 60)}"  # 按分钟分组
        
        if pattern_key not in self.preload_patterns:
            self.preload_patterns[pattern_key] = []
        
        self.preload_patterns[pattern_key].append(key)
        
        # 保持最近10分钟的模式
        cutoff_time = current_time - 600  # 10分钟前
        expired_patterns = [
            k for k in self.preload_patterns.keys() 
            if int(k.split('_')[1]) * 60 < cutoff_time
        ]
        for k in expired_patterns:
            del self.preload_patterns[k]
    
    def _start_cleanup_task(self):
        """启动清理任务"""
        def cleanup_worker():
            while True:
                try:
                    time.sleep(self.config.cleanup_interval)
                    self._cleanup_expired()
                except Exception as e:
                    logger.error(f"缓存清理任务失败: {e}")
        
        cleanup_thread = threading.Thread(target=cleanup_worker, daemon=True)
        cleanup_thread.start()
    
    def _cleanup_expired(self):
        """清理过期缓存"""
        with self.lock:
            current_time = time.time()
            
            # 清理内存中的过期项
            expired_keys = []
            for key, item in self.memory_cache.items():
                if item.is_expired():
                    expired_keys.append(key)
            
            for key in expired_keys:
                self._remove_from_memory(key)
            
            # 清理磁盘中的过期项
            expired_disk_keys = []
            for key, info in self.disk_index.items():
                if info.get('ttl') and current_time - info['created'] > info['ttl']:
                    expired_disk_keys.append(key)
            
            for key in expired_disk_keys:
                self._remove_from_disk(key)
            
            if expired_keys or expired_disk_keys:
                logger.info(f"缓存清理完成: 内存 {len(expired_keys)} 项, 磁盘 {len(expired_disk_keys)} 项")
    
    def get_stats(self) -> Dict[str, Any]:
        """获取缓存统计信息"""
        with self.lock:
            total_requests = self.stats['hits'] + self.stats['misses']
            hit_rate = (self.stats['hits'] / total_requests * 100) if total_requests > 0 else 0
            
            return {
                'name': self.name,
                'memory_items': len(self.memory_cache),
                'memory_size_mb': self.memory_size / 1024 / 1024,
                'disk_items': len(self.disk_index),
                'hit_rate': hit_rate,
                'stats': self.stats.copy()
            }


# 全局缓存管理器
_cache_managers: Dict[str, SmartCache] = {}
_cache_lock = threading.Lock()


def get_cache(name: str = "default", config: Optional[CacheConfig] = None) -> SmartCache:
    """获取缓存管理器实例"""
    with _cache_lock:
        if name not in _cache_managers:
            _cache_managers[name] = SmartCache(name, config)
        return _cache_managers[name]


# 便捷函数
def cache_get(key: str, default: Any = None, cache_name: str = "default") -> Any:
    """获取缓存"""
    return get_cache(cache_name).get(key, default)


def cache_put(key: str, data: Any, ttl: Optional[int] = None, cache_name: str = "default") -> bool:
    """存储缓存"""
    return get_cache(cache_name).put(key, data, ttl)


def cache_remove(key: str, cache_name: str = "default") -> bool:
    """删除缓存"""
    return get_cache(cache_name).remove(key)


def cache_clear(cache_name: str = "default"):
    """清空缓存"""
    get_cache(cache_name).clear()
