#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试aiohttp对国内API的连接问题
"""

import asyncio
import aiohttp
import json
import time

async def test_aiohttp_domestic():
    """使用aiohttp测试国内API"""
    print("=" * 60)
    print("aiohttp国内API连接测试")
    print("=" * 60)
    
    # 从配置文件读取API配置
    try:
        with open('config/llm_config.json', 'r', encoding='utf-8') as f:
            config = json.load(f)
        models = config.get('models', [])
    except Exception as e:
        print(f"❌ 无法读取配置文件: {e}")
        return
    
    domestic_apis = []
    for model in models:
        if model['type'] in ['zhipu', 'tongyi', 'deepseek']:
            domestic_apis.append(model)
    
    for api_config in domestic_apis:
        name = api_config['name']
        api_type = api_config['type']
        url = api_config['url']
        key = api_config['key']
        
        print(f"\n🔍 测试 {name} ({api_type}):")
        
        # 测试1: 禁用代理的aiohttp连接
        print(f"   🧪 测试1: aiohttp禁用代理")
        try:
            headers = {
                'Authorization': f'Bearer {key}',
                'Content-Type': 'application/json',
                'User-Agent': 'AI-Video-Generator/1.0'
            }
            
            model_names = {
                'zhipu': 'glm-4-flash',
                'tongyi': 'qwen-plus',
                'deepseek': 'deepseek-chat'
            }
            
            data = {
                'model': model_names[api_type],
                'messages': [{'role': 'user', 'content': '你好，请简单回复'}],
                'max_tokens': 20
            }
            
            # 创建连接器，禁用代理
            connector = aiohttp.TCPConnector(use_dns_cache=False)
            timeout = aiohttp.ClientTimeout(total=30)
            
            start_time = time.time()
            async with aiohttp.ClientSession(connector=connector) as session:
                async with session.post(
                    url,
                    headers=headers,
                    json=data,
                    timeout=timeout,
                    proxy=None  # 禁用代理
                ) as response:
                    end_time = time.time()
                    
                    if response.status == 200:
                        result = await response.json()
                        content = result['choices'][0]['message']['content']
                        print(f"      ✅ 成功 ({end_time-start_time:.2f}s): {content[:30]}...")
                    else:
                        error_text = await response.text()
                        print(f"      ❌ 失败，状态码: {response.status}")
                        print(f"      响应: {error_text[:200]}...")
                        
        except asyncio.TimeoutError:
            print(f"      ❌ aiohttp超时（30秒）")
        except Exception as e:
            print(f"      ❌ aiohttp错误: {e}")
        
        # 测试2: 使用系统代理的aiohttp连接
        print(f"   🧪 测试2: aiohttp使用系统代理")
        try:
            # 获取系统代理
            import winreg
            system_proxy = None
            try:
                with winreg.OpenKey(winreg.HKEY_CURRENT_USER, 
                                   r"Software\Microsoft\Windows\CurrentVersion\Internet Settings") as reg_key:
                    proxy_enable = winreg.QueryValueEx(reg_key, "ProxyEnable")[0]
                    if proxy_enable:
                        proxy_server = winreg.QueryValueEx(reg_key, "ProxyServer")[0]
                        if not proxy_server.startswith('http'):
                            system_proxy = f"http://{proxy_server}"
                        else:
                            system_proxy = proxy_server
            except:
                pass
            
            connector = aiohttp.TCPConnector(use_dns_cache=False)
            timeout = aiohttp.ClientTimeout(total=30)
            
            start_time = time.time()
            async with aiohttp.ClientSession(connector=connector) as session:
                async with session.post(
                    url,
                    headers=headers,
                    json=data,
                    timeout=timeout,
                    proxy=system_proxy  # 使用系统代理
                ) as response:
                    end_time = time.time()
                    
                    if response.status == 200:
                        result = await response.json()
                        content = result['choices'][0]['message']['content']
                        print(f"      ✅ 成功 ({end_time-start_time:.2f}s): {content[:30]}...")
                    else:
                        error_text = await response.text()
                        print(f"      ❌ 失败，状态码: {response.status}")
                        print(f"      响应: {error_text[:200]}...")
                        
        except asyncio.TimeoutError:
            print(f"      ❌ aiohttp超时（30秒）")
        except Exception as e:
            print(f"      ❌ aiohttp错误: {e}")
        
        # 测试3: 模拟程序实际调用
        print(f"   🧪 测试3: 模拟程序实际调用")
        try:
            # 使用与程序相同的配置
            connector = aiohttp.TCPConnector(
                use_dns_cache=False,
                ttl_dns_cache=300,
                limit=100,
                limit_per_host=30
            )
            timeout = aiohttp.ClientTimeout(
                total=90,  # 增加超时时间
                connect=30,
                sock_read=30
            )
            
            start_time = time.time()
            async with aiohttp.ClientSession(connector=connector) as session:
                async with session.post(
                    url,
                    headers=headers,
                    json=data,
                    timeout=timeout,
                    proxy=None  # 国内API禁用代理
                ) as response:
                    end_time = time.time()
                    
                    if response.status == 200:
                        result = await response.json()
                        content = result['choices'][0]['message']['content']
                        print(f"      ✅ 成功 ({end_time-start_time:.2f}s): {content[:30]}...")
                    else:
                        error_text = await response.text()
                        print(f"      ❌ 失败，状态码: {response.status}")
                        print(f"      响应: {error_text[:200]}...")
                        
        except asyncio.TimeoutError:
            print(f"      ❌ 程序模拟超时（90秒）")
        except Exception as e:
            print(f"      ❌ 程序模拟错误: {e}")

if __name__ == "__main__":
    asyncio.run(test_aiohttp_domestic())
