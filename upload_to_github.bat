@echo off
chcp 65001 >nul
echo ============================================================
echo 🎬 AI视频生成器 - GitHub自动上传工具
echo ============================================================
echo.
echo ⚠️ 警告: 此操作将完全清理并重新上传GitHub仓库
echo ⚠️ 远程仓库的所有现有内容将被替换
echo.

set repo_url=https://github.com/ljc31555/A2.git
echo 🎯 目标仓库: %repo_url%
echo 👤 GitHub用户: ljc31555

echo.
set /p confirm="确认要继续吗? 这将清理远程仓库的所有内容 (y/N): "
if /i not "%confirm%"=="y" if /i not "%confirm%"=="yes" (
    echo ❌ 操作已取消
    pause
    exit /b 1
)

echo.
echo 🚀 开始自动上传流程...
echo.

echo [1/7] 清理Git历史...
if exist .git rmdir /s /q .git
echo ✅ Git历史已清理

echo.
echo [2/7] 运行安全检查...
python simple_security_check.py
if errorlevel 1 (
    echo ❌ 安全检查失败
    pause
    exit /b 1
)
echo ✅ 安全检查通过

echo.
echo [3/7] 清理项目文件...
python cleanup.py
echo ✅ 项目清理完成

echo.
echo [4/7] 初始化Git仓库...
git init
if errorlevel 1 (
    echo ❌ Git初始化失败
    pause
    exit /b 1
)
echo ✅ Git仓库初始化完成

echo.
echo [5/7] 添加远程仓库...
git remote add origin %repo_url%
if errorlevel 1 (
    echo ❌ 添加远程仓库失败
    pause
    exit /b 1
)
echo ✅ 远程仓库添加完成

echo.
echo [6/7] 添加和提交文件...
git add .
if errorlevel 1 (
    echo ❌ 添加文件失败
    pause
    exit /b 1
)

git commit -m "🎬 AI视频生成器 - 完整项目重新上传

✨ 功能特性:
- 🎨 五阶段分镜生成系统
- 🤖 多AI服务支持 (DeepSeek、通义千问、智谱AI等)
- 🖼️ 图像生成引擎 (Pollinations、ComfyUI、DALL-E等)
- 🎙️ 语音服务 (Azure TTS、OpenAI TTS等)
- 💻 现代化PyQt5界面
- 📁 完整项目管理系统
- ⚡ 异步处理架构
- 🔧 模块化设计

🔧 技术栈:
- Python 3.8+
- PyQt5界面框架
- 多种AI服务集成
- 异步编程架构
- 模块化设计

🔐 安全特性:
- ✅ 无真实API密钥泄露
- ✅ 完整的配置模板
- ✅ 敏感信息保护"

if errorlevel 1 (
    echo ❌ 提交失败
    pause
    exit /b 1
)
echo ✅ 文件提交完成

echo.
echo [7/7] 强制推送到GitHub...
git push -f origin main
if errorlevel 1 (
    echo 尝试推送到master分支...
    git push -f origin master
    if errorlevel 1 (
        echo ❌ 推送失败，请检查仓库URL和网络连接
        pause
        exit /b 1
    )
)

echo.
echo ============================================================
echo 🎉 AI视频生成器已成功上传到GitHub!
echo ============================================================
echo 📋 上传摘要:
echo ✅ 远程仓库已完全清理
echo ✅ 项目文件已重新上传
echo ✅ 安全检查通过
echo ✅ 敏感信息已保护
echo.
echo 🔗 仓库链接: %repo_url%
echo.
echo 📖 下一步建议:
echo 1. 访问GitHub仓库页面
echo 2. 设置仓库描述:
echo    🎬 AI视频生成器 - 功能强大的AI驱动视频生成工具
echo.
echo 3. 添加标签 (Topics):
echo    ai, video-generation, python, pyqt5, llm, image-generation
echo.
echo 4. 考虑创建Release版本
echo 5. 启用Issues和Discussions
echo ============================================================

pause
