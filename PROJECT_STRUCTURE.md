# AI视频生成器 - 项目结构说明 📁

## 📋 项目概览

本文档详细说明了AI视频生成器项目的文件结构和各部分的功能。

## 🗂️ 根目录文件

### 📖 文档文件
- `README.md` - 项目主要说明文档
- `QUICK_START_NEW.md` - 快速开始指南
- `CONSISTENCY_SYSTEM_GUIDE.md` - 一致性系统使用指南
- `PROJECT_STRUCTURE.md` - 本文件，项目结构说明

### 🚀 启动和安装
- `main.py` - 主程序入口文件
- `start.bat` - Windows快速启动脚本
- `install.py` - 自动安装脚本
- `setup_environment.py` - 环境设置脚本

### 📦 依赖管理
- `requirements.txt` - 完整依赖列表
- `requirements_minimal.txt` - 最小化依赖列表

### 🧪 测试和演示
- `simple_demo.py` - 简单功能演示脚本
- `run_tests.py` - 测试运行脚本

### 🧹 维护工具
- `cleanup.py` - 项目清理脚本

## 📁 主要目录结构

### `src/` - 源代码目录
```
src/
├── __init__.py                    # 包初始化文件
├── core/                          # 核心架构层
│   ├── api_manager.py            # API管理器
│   ├── service_base.py           # 服务基类
│   ├── service_manager.py        # 服务管理器
│   └── app_controller.py         # 应用控制器
├── services/                      # AI服务层
│   ├── llm_service.py            # 大语言模型服务
│   ├── image_service.py          # 图像生成服务
│   └── voice_service.py          # 语音服务
├── processors/                    # 业务处理层
│   ├── text_processor.py         # 文本处理器
│   ├── image_processor.py        # 图像处理器
│   └── video_processor.py        # 视频处理器
├── gui/                          # 用户界面层
│   ├── new_main_window.py        # 主窗口
│   ├── five_stage_storyboard_tab.py  # 五阶段分镜
│   ├── ai_drawing_tab.py         # AI绘图界面
│   ├── consistency_control_panel.py # 一致性控制
│   └── ...                       # 其他界面组件
├── models/                       # 数据模型和引擎
│   ├── engines/                  # 图像生成引擎
│   ├── image_generation_service.py
│   └── ...
├── utils/                        # 工具模块
├── audio_processing/             # 音频处理模块
├── video_processing/             # 视频处理模块
├── batch_processing/             # 批量处理模块
└── character_scene_db/           # 角色场景数据库
```

### `config/` - 配置文件目录
```
config/
├── app_settings.example.json     # 应用配置示例
├── llm_config.example.json      # LLM配置示例
├── llm_config.json              # LLM配置文件
├── tts_config.example.json      # TTS配置示例
├── image_generation_config.py   # 图像生成配置
├── baidu_translate_config.example.py # 百度翻译配置示例
└── workflows/                   # ComfyUI工作流
    ├── README.md
    └── flux.1-dev.json
```

### `tests/` - 测试文件目录
```
tests/
├── __init__.py                   # 测试包初始化
├── pytest.ini                   # pytest配置
├── requirements.txt              # 测试依赖
├── test_config_manager.py        # 配置管理器测试
├── test_error_handler.py         # 错误处理测试
├── test_integration.py           # 集成测试
├── test_runner.py                # 测试运行器
└── test_service_manager.py       # 服务管理器测试
```

### `assets/` - 资源文件目录
```
assets/
├── app_icon.png                  # 应用图标
└── styles.qss                    # Qt样式表
```

### `output/` - 输出目录
```
output/                           # 生成内容输出目录
├── images/                       # 生成的图像
├── videos/                       # 生成的视频
├── audio/                        # 生成的音频
└── projects/                     # 项目文件
```

### `logs/` - 日志目录
```
logs/
└── aivideologger.log            # 应用日志文件
```

### `temp/` - 临时文件目录
```
temp/
├── cache/                        # 通用缓存
└── image_cache/                  # 图像缓存
```

### `venv/` - 虚拟环境目录
```
venv/                            # Python虚拟环境
├── Include/                     # 头文件
├── Lib/                         # 库文件
├── Scripts/                     # 可执行文件
└── pyvenv.cfg                   # 虚拟环境配置
```

## 🔧 核心功能模块说明

### 1. 核心架构层 (`src/core/`)
- **API管理器**: 统一管理各种AI服务的API调用
- **服务基类**: 为所有AI服务提供统一的接口
- **服务管理器**: 管理服务生命周期和工作流编排
- **应用控制器**: 协调各个组件的工作

### 2. AI服务层 (`src/services/`)
- **LLM服务**: 支持多种大语言模型提供商
- **图像服务**: 集成多种图像生成引擎
- **语音服务**: 提供TTS和STT功能

### 3. 处理器层 (`src/processors/`)
- **文本处理器**: 处理文本改写、分镜生成等
- **图像处理器**: 处理图像生成、批量处理等
- **视频处理器**: 处理视频合成、剪辑等

### 4. 界面层 (`src/gui/`)
- **主窗口**: 应用程序的主界面
- **功能标签页**: 各种功能的专用界面
- **控制面板**: 参数配置和控制界面

## 📝 配置文件说明

### 必需配置
- `config/app_settings.json` - 主要应用配置
- `config/llm_config.json` - LLM服务配置

### 可选配置
- `config/tts_config.json` - 语音服务配置
- `config/baidu_translate_config.py` - 翻译服务配置

### 示例配置
所有 `.example.*` 文件都是配置模板，复制并重命名后填入实际配置。

## 🚀 扩展开发指南

### 添加新的AI服务
1. 在 `src/services/` 创建新的服务类
2. 继承 `ServiceBase` 基类
3. 在 `ServiceManager` 中注册新服务

### 添加新的处理器
1. 在 `src/processors/` 创建新的处理器类
2. 实现必要的处理逻辑
3. 在相应的界面中集成

### 添加新的界面组件
1. 在 `src/gui/` 创建新的界面类
2. 继承适当的PyQt5基类
3. 在主窗口中集成新组件

## 🧹 维护和清理

### 定期清理
运行 `python cleanup.py` 清理：
- Python缓存文件
- 临时文件
- 过期的图像缓存
- 测试缓存文件

### 日志管理
- 日志文件位于 `logs/` 目录
- 可以通过清理脚本管理日志文件
- 支持保留最新日志的清理模式

## 📊 项目统计

### 代码结构
- **核心模块**: 4个主要层次
- **AI服务**: 支持10+种服务提供商
- **界面组件**: 20+个功能界面
- **配置文件**: 灵活的配置管理系统

### 功能特性
- **异步处理**: 全面支持异步操作
- **模块化设计**: 高度解耦的架构
- **扩展性**: 易于添加新功能
- **维护性**: 完善的日志和错误处理

---

📝 **说明**: 此文档会随着项目发展持续更新。如有疑问，请参考相关源代码或联系开发团队。
