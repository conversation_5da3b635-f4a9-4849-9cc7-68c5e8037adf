#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试场景描述增强修复
验证ContentFuser类中_call_llm_for_enhancement方法是否正确添加
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.utils.logger import logger

def test_content_fuser_method():
    """测试ContentFuser类是否包含_call_llm_for_enhancement方法"""
    print("🔧 测试ContentFuser类方法...")
    
    try:
        from src.processors.scene_description_enhancer import ContentFuser
        
        # 创建ContentFuser实例
        fuser = ContentFuser()
        
        # 检查方法是否存在
        if hasattr(fuser, '_call_llm_for_enhancement'):
            print("✅ _call_llm_for_enhancement方法已存在")
            
            # 检查方法是否可调用
            if callable(getattr(fuser, '_call_llm_for_enhancement')):
                print("✅ _call_llm_for_enhancement方法可调用")
                return True
            else:
                print("❌ _call_llm_for_enhancement不是可调用方法")
                return False
        else:
            print("❌ _call_llm_for_enhancement方法不存在")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_method_signature():
    """测试方法签名是否正确"""
    print("\n🔍 测试方法签名...")
    
    try:
        from src.processors.scene_description_enhancer import ContentFuser
        import inspect
        
        fuser = ContentFuser()
        method = getattr(fuser, '_call_llm_for_enhancement')
        
        # 获取方法签名
        sig = inspect.signature(method)
        params = list(sig.parameters.keys())
        
        print(f"方法参数: {params}")
        
        # 检查是否有enhancement_prompt参数
        if 'enhancement_prompt' in params:
            print("✅ enhancement_prompt参数存在")
            return True
        else:
            print("❌ enhancement_prompt参数缺失")
            return False
            
    except Exception as e:
        print(f"❌ 测试方法签名失败: {e}")
        return False

def test_mock_enhancement():
    """测试模拟增强调用"""
    print("\n🧪 测试模拟增强调用...")
    
    try:
        from src.processors.scene_description_enhancer import ContentFuser
        
        # 创建ContentFuser实例（不需要真实的LLM API）
        fuser = ContentFuser()
        
        # 模拟增强提示
        test_prompt = """请对以下画面描述进行智能增强：
原始描述：牛顿站在天体运动模型前，手指着模型，表情中充满自豪。
技术细节补充：中景镜头; 侧面; 侧光; 跟随; 对角线
请输出增强后的画面描述："""
        
        # 调用方法（应该会返回原始提示作为备用）
        result = fuser._call_llm_for_enhancement(test_prompt)
        
        if result:
            print("✅ 方法调用成功")
            print(f"返回结果长度: {len(result)} 字符")
            print(f"返回结果预览: {result[:100]}...")
            return True
        else:
            print("❌ 方法返回空结果")
            return False
            
    except Exception as e:
        print(f"❌ 模拟增强调用失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_llm_enhanced_fusion():
    """测试LLM增强融合是否能正常调用"""
    print("\n🎯 测试LLM增强融合...")
    
    try:
        from src.processors.scene_description_enhancer import ContentFuser, FusionResult
        
        # 创建ContentFuser实例
        fuser = ContentFuser()
        
        # 模拟内容数据
        content = {
            'original': '牛顿站在天体运动模型前，手指着模型，表情中充满自豪。',
            'technical_parts': ['中景镜头', '侧面', '侧光'],
            'consistency_parts': [],
            'detected_characters': ['牛顿'],
            'detected_scenes': [],
            'original_length': 30,
            'has_punctuation': True,
            'style': '写实摄影风格'
        }
        
        # 调用LLM增强融合（应该会回退到自然融合）
        result = fuser._llm_enhanced_fusion(content)
        
        if isinstance(result, FusionResult):
            print("✅ LLM增强融合调用成功")
            print(f"增强结果: {result.enhanced_description[:100]}...")
            print(f"质量评分: {result.fusion_quality_score}")
            return True
        else:
            print("❌ LLM增强融合返回类型错误")
            return False
            
    except Exception as e:
        print(f"❌ LLM增强融合测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🔧 场景描述增强修复测试")
    print("=" * 50)
    
    # 测试1：检查方法是否存在
    test1 = test_content_fuser_method()
    
    # 测试2：检查方法签名
    test2 = test_method_signature()
    
    # 测试3：模拟增强调用
    test3 = test_mock_enhancement()
    
    # 测试4：测试LLM增强融合
    test4 = test_llm_enhanced_fusion()
    
    print("\n" + "=" * 50)
    print("📊 测试结果总结:")
    print(f"1. 方法存在性检查: {'✅ 通过' if test1 else '❌ 失败'}")
    print(f"2. 方法签名检查: {'✅ 通过' if test2 else '❌ 失败'}")
    print(f"3. 模拟增强调用: {'✅ 通过' if test3 else '❌ 失败'}")
    print(f"4. LLM增强融合: {'✅ 通过' if test4 else '❌ 失败'}")
    
    all_passed = test1 and test2 and test3 and test4
    
    if all_passed:
        print("\n🎉 所有测试通过！修复成功！")
        print("\n📋 修复说明:")
        print("1. ✅ 在ContentFuser类中添加了_call_llm_for_enhancement方法")
        print("2. ✅ 方法签名正确，包含enhancement_prompt参数")
        print("3. ✅ 方法可以正常调用，提供备用处理机制")
        print("4. ✅ LLM增强融合流程可以正常工作")
        print("\n🎯 预期效果:")
        print("- 增强描述功能不再出现'ContentFuser' object has no attribute '_call_llm_for_enhancement'错误")
        print("- 即使LLM API不可用，也会提供备用处理方案")
        print("- 场景描述增强功能可以正常工作")
    else:
        print("\n❌ 部分测试失败，需要进一步检查")

if __name__ == "__main__":
    main()
