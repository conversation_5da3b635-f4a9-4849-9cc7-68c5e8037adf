#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI视频生成器 - 修复版上传脚本
专为 ljc31555/A2 仓库定制
"""

import os
import subprocess
import sys
import shutil
import time
from pathlib import Path

# 仓库配置
REPO_URL = "https://github.com/ljc31555/A2.git"
USERNAME = "ljc31555"
REPO_NAME = "A2"

def print_step(step_num, total_steps, description):
    """打印步骤信息"""
    print(f"\n[{step_num}/{total_steps}] {description}")
    print("-" * 50)

def run_command(command, description="", ignore_error=False):
    """运行命令并处理结果"""
    print(f"执行: {command}")
    try:
        # 使用gbk编码处理中文输出
        result = subprocess.run(command, shell=True, capture_output=True, text=True, encoding='gbk', errors='ignore')
        if result.stdout:
            print(f"输出: {result.stdout.strip()}")
        if result.stderr and result.returncode != 0 and not ignore_error:
            print(f"错误: {result.stderr.strip()}")
        return result.returncode == 0 or ignore_error
    except Exception as e:
        print(f"命令执行异常: {e}")
        return ignore_error

def force_remove_git():
    """强制删除Git目录"""
    print("尝试强制删除Git历史...")
    git_dir = Path(".git")
    if git_dir.exists():
        try:
            # 尝试修改权限后删除
            os.system('attrib -R .git\\*.* /S')
            shutil.rmtree(git_dir, ignore_errors=True)
            time.sleep(1)
            if git_dir.exists():
                # 如果还存在，使用系统命令强制删除
                os.system('rmdir /s /q .git')
            print("✅ Git历史删除成功")
            return True
        except Exception as e:
            print(f"⚠️ Git历史删除失败: {e}")
            print("ℹ️ 将重新初始化现有仓库")
            return False
    else:
        print("ℹ️ 没有现有Git历史")
        return True

def main():
    """主上传流程"""
    print("=" * 60)
    print("AI视频生成器 - 修复版上传到 ljc31555/A2")
    print("=" * 60)
    print(f"目标仓库: {REPO_URL}")
    print(f"GitHub用户: {USERNAME}")
    print()
    print("警告: 此操作将完全清理并重新上传GitHub仓库")
    print("远程仓库的所有现有内容将被替换")
    print()
    
    # 确认操作
    confirm = input("确认要继续吗? (y/N): ").strip().lower()
    if confirm not in ['y', 'yes']:
        print("操作已取消")
        return False
    
    print("\n开始自动上传流程...")
    
    # 步骤1: 清理Git历史
    print_step(1, 7, "清理现有Git历史")
    force_remove_git()
    
    # 步骤2: 安全检查
    print_step(2, 7, "运行安全检查")
    # 简化安全检查，避免编码问题
    sensitive_files = [
        'config/app_settings.json',
        'config/llm_config.json', 
        'config/tts_config.json',
        'config/baidu_translate_config.py'
    ]
    
    found_sensitive = False
    for file_path in sensitive_files:
        if os.path.exists(file_path):
            print(f"发现敏感文件: {file_path}")
            found_sensitive = True
        else:
            print(f"敏感文件已排除: {file_path}")
    
    if found_sensitive:
        print("❌ 发现敏感文件，请先删除")
        return False
    else:
        print("✅ 安全检查通过")
    
    # 步骤3: 清理项目
    print_step(3, 7, "清理项目文件")
    # 手动清理，避免脚本编码问题
    cache_dirs = ['.pytest_cache', '__pycache__']
    for cache_dir in cache_dirs:
        if os.path.exists(cache_dir):
            try:
                shutil.rmtree(cache_dir)
                print(f"删除缓存目录: {cache_dir}")
            except:
                pass
    print("✅ 项目清理完成")
    
    # 步骤4: 初始化Git
    print_step(4, 7, "初始化Git仓库")
    if not run_command("git init"):
        print("❌ Git初始化失败")
        return False
    print("✅ Git仓库初始化完成")
    
    # 步骤5: 处理远程仓库
    print_step(5, 7, f"配置远程仓库: {REPO_URL}")
    
    # 先尝试删除现有的origin
    run_command("git remote remove origin", ignore_error=True)
    
    # 添加远程仓库
    if not run_command(f"git remote add origin {REPO_URL}"):
        print("❌ 添加远程仓库失败")
        return False
    print("✅ 远程仓库配置完成")
    
    # 步骤6: 提交文件
    print_step(6, 7, "添加和提交文件")
    
    # 添加所有文件
    if not run_command("git add ."):
        print("❌ 添加文件失败")
        return False
    
    # 提交文件
    commit_message = "AI视频生成器 - 完整项目重新上传"
    
    if not run_command(f'git commit -m "{commit_message}"'):
        print("❌ 提交失败")
        return False
    print("✅ 文件提交完成")
    
    # 步骤7: 强制推送
    print_step(7, 7, "强制推送到GitHub")
    print("这将完全替换远程仓库的所有内容")
    
    # 设置默认分支并强制推送
    run_command("git branch -M main", ignore_error=True)
    
    if not run_command("git push -f origin main"):
        # 如果main分支失败，尝试master分支
        print("尝试推送到master分支...")
        if not run_command("git push -f origin master"):
            print("❌ 推送失败")
            print("可能的原因:")
            print("1. 需要GitHub身份验证")
            print("2. 网络连接问题")
            print("3. 仓库权限问题")
            print()
            print("解决方案:")
            print("1. 配置GitHub Personal Access Token")
            print("2. 检查网络连接")
            print("3. 确认仓库访问权限")
            return False
    
    print("✅ 强制推送完成")
    
    # 显示完成信息
    print("\n" + "=" * 60)
    print("AI视频生成器已成功上传到GitHub!")
    print("=" * 60)
    print("上传摘要:")
    print("✅ 远程仓库已完全清理")
    print("✅ 项目文件已重新上传")
    print("✅ 安全检查通过")
    print("✅ 敏感信息已保护")
    print()
    print("仓库链接:")
    print(f"   https://github.com/{USERNAME}/{REPO_NAME}")
    print()
    print("下一步建议:")
    print("1. 访问GitHub仓库页面")
    print("2. 设置仓库描述")
    print("3. 添加标签 (Topics)")
    print("4. 创建Release版本")
    print("5. 启用Issues和Discussions")
    print("=" * 60)
    
    return True

if __name__ == "__main__":
    try:
        success = main()
        if success:
            print("\n恭喜！您的AI视频生成器项目已成功发布到GitHub！")
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n操作被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n上传过程中发生错误: {e}")
        sys.exit(1)
