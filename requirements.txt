# AI视频生成器 - 完整依赖列表
# 支持Python 3.8+
# 最后更新: 2025-06-17

# ==================== 核心框架 ====================
# GUI框架 - PyQt5完整套件
PyQt5>=5.15.0
PyQtWebEngine>=5.15.0

# 异步HTTP和文件操作
aiohttp>=3.8.0
aiofiles>=0.8.0

# 同步HTTP客户端
requests>=2.28.0
httpx>=0.23.0

# WebSocket支持
websockets>=10.3

# ==================== 图像处理 ====================
# 基础图像处理
Pillow>=9.0.0
numpy>=1.21.0

# 计算机视觉
opencv-python>=4.6.0

# ==================== 视频处理 ====================
# 视频编辑和处理
moviepy>=1.0.3
ffmpeg-python>=0.2.0

# ==================== 音频处理 ====================
# 音频处理和格式转换
pydub>=0.25.1

# 语音识别 (可选，某些系统可能安装失败)
# SpeechRecognition>=3.8.1

# ==================== AI/ML相关 ====================
# 大语言模型API客户端
openai>=1.0.0
anthropic>=0.3.0

# 机器学习框架
transformers>=4.21.0
torch>=1.12.0

# Hugging Face生态
huggingface-hub>=0.16.0
tokenizers>=0.13.0
safetensors>=0.3.0

# ==================== 数据处理 ====================
# 数据分析
pandas>=1.4.0

# 配置文件处理
pyyaml>=6.0
json5>=0.9.0

# ==================== 文本处理 ====================
# 中文分词
jieba>=0.42.0

# 正则表达式增强
regex>=2022.0.0

# ==================== 网络和API ====================
# HTTP客户端增强
urllib3>=1.26.0
certifi>=2022.0.0

# JSON解析加速
jiter>=0.1.0

# 系统信息
distro>=1.7.0

# ==================== 工具库 ====================
# 进度条
tqdm>=4.64.0

# 终端颜色
colorama>=0.4.5

# 命令行工具
click>=8.1.0

# 环境变量管理
python-dotenv>=0.19.0

# 时间处理
python-dateutil>=2.8.0

# 文件系统操作
fsspec>=2023.1.0

# 数学计算
sympy>=1.11.0
mpmath>=1.2.0

# 网络工具
networkx>=2.8.0

# ==================== 系统集成 ====================
# 系统信息监控
psutil>=5.9.0

# 异步支持
anyio>=3.6.0
sniffio>=1.3.0

# 类型检查和注解
typing-extensions>=4.3.0
annotated-types>=0.4.0

# 数据验证
pydantic>=2.0.0

# ==================== 开发和测试工具 ====================
# 测试框架
pytest>=7.1.0
pytest-asyncio>=0.19.0
pytest-cov>=3.0.0

# 代码格式化
black>=22.6.0
isort>=5.10.0

# 代码检查
flake8>=5.0.0

# ==================== 运行时依赖 ====================
# 确保兼容性
setuptools>=65.0.0
wheel>=0.37.0

# 包管理
packaging>=21.0.0
filelock>=3.8.0

# 标记和模板
MarkupSafe>=2.1.0
Jinja2>=3.1.0

# 字符编码
charset-normalizer>=3.0.0
idna>=3.4.0

# HTTP核心
h11>=0.14.0
httpcore>=0.16.0

# 数据类支持 (Python < 3.7)
# dataclasses>=0.8; python_version<"3.7"

# ==================== 可选依赖说明 ====================
# 以下依赖根据具体需求安装，已注释避免安装失败：
#
# Azure认知服务 (语音服务):
# azure-cognitiveservices-speech>=1.22.0
#
# ElevenLabs API:
# elevenlabs>=0.2.0
#
# Stability AI SDK:
# stability-sdk>=0.3.0
#
# Google Cloud AI:
# google-cloud-aiplatform>=1.25.0
#
# ComfyUI客户端:
# websocket-client>=1.3.0
#
# 高级图像处理:
# scikit-image>=0.19.0
#
# 深度学习加速 (CUDA支持):
# torch-audio>=0.12.0
# torchvision>=0.13.0
#
# 语音识别:
# SpeechRecognition>=3.8.1
# pyaudio>=0.2.11  # 需要额外的系统依赖
#
# 数据库支持:
# sqlalchemy>=1.4.0
# sqlite3  # Python内置
#
# 缓存支持:
# redis>=4.3.0
# memcached>=1.59
#
# 消息队列:
# celery>=5.2.0
# kombu>=5.2.0

# ==================== 安装说明 ====================
# 1. 基础安装:
#    pip install -r requirements.txt
#
# 2. 开发环境安装:
#    pip install -r requirements.txt -r tests/requirements.txt
#
# 3. 最小化安装:
#    pip install -r requirements_minimal.txt
#
# 4. 如果某些包安装失败，可以跳过继续安装其他包:
#    pip install -r requirements.txt --ignore-installed --no-deps
#    然后手动安装失败的包
#
# 5. 使用国内镜像加速安装:
#    pip install -r requirements.txt -i https://pypi.tuna.tsinghua.edu.cn/simple/
