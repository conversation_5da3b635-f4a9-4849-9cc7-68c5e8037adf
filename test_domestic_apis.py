#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
专门测试国内API的连接问题
"""

import requests
import json
import time

def test_domestic_apis():
    """测试国内API连接"""
    print("=" * 60)
    print("国内API连接问题诊断")
    print("=" * 60)
    
    # 从配置文件读取API配置
    try:
        with open('config/llm_config.json', 'r', encoding='utf-8') as f:
            config = json.load(f)
        models = config.get('models', [])
    except Exception as e:
        print(f"❌ 无法读取配置文件: {e}")
        return
    
    domestic_apis = []
    for model in models:
        if model['type'] in ['zhipu', 'tongyi', 'deepseek']:
            domestic_apis.append(model)
    
    for api_config in domestic_apis:
        name = api_config['name']
        api_type = api_config['type']
        url = api_config['url']
        key = api_config['key']
        
        print(f"\n🔍 测试 {name} ({api_type}):")
        print(f"   URL: {url}")
        
        # 测试1: 禁用代理的连接
        print(f"   🧪 测试1: 禁用代理连接")
        try:
            # 构造测试请求
            headers = {
                'Authorization': f'Bearer {key}',
                'Content-Type': 'application/json',
                'User-Agent': 'AI-Video-Generator/1.0'
            }
            
            model_names = {
                'zhipu': 'glm-4-flash',
                'tongyi': 'qwen-plus',
                'deepseek': 'deepseek-chat'
            }
            
            data = {
                'model': model_names[api_type],
                'messages': [{'role': 'user', 'content': '你好，请简单回复'}],
                'max_tokens': 20
            }
            
            start_time = time.time()
            response = requests.post(
                url,
                json=data,
                headers=headers,
                timeout=30,
                proxies={"http": None, "https": None}  # 明确禁用代理
            )
            end_time = time.time()
            
            if response.status_code == 200:
                result = response.json()
                content = result['choices'][0]['message']['content']
                print(f"      ✅ 成功 ({end_time-start_time:.2f}s): {content[:30]}...")
            else:
                print(f"      ❌ 失败，状态码: {response.status_code}")
                print(f"      响应: {response.text[:200]}...")
                
        except requests.exceptions.Timeout:
            print(f"      ❌ 超时（30秒）")
        except requests.exceptions.ConnectionError as e:
            print(f"      ❌ 连接错误: {e}")
        except Exception as e:
            print(f"      ❌ 其他错误: {e}")
        
        # 测试2: 使用系统代理的连接
        print(f"   🧪 测试2: 使用系统代理连接")
        try:
            start_time = time.time()
            response = requests.post(
                url,
                json=data,
                headers=headers,
                timeout=30
                # 使用系统代理
            )
            end_time = time.time()
            
            if response.status_code == 200:
                result = response.json()
                content = result['choices'][0]['message']['content']
                print(f"      ✅ 成功 ({end_time-start_time:.2f}s): {content[:30]}...")
            else:
                print(f"      ❌ 失败，状态码: {response.status_code}")
                print(f"      响应: {response.text[:200]}...")
                
        except requests.exceptions.Timeout:
            print(f"      ❌ 超时（30秒）")
        except requests.exceptions.ConnectionError as e:
            print(f"      ❌ 连接错误: {e}")
        except Exception as e:
            print(f"      ❌ 其他错误: {e}")
        
        # 测试3: 基础连接测试
        print(f"   🧪 测试3: 基础连接测试")
        try:
            from urllib.parse import urlparse
            parsed = urlparse(url)
            base_url = f"{parsed.scheme}://{parsed.netloc}"
            
            response = requests.get(
                base_url,
                timeout=10,
                proxies={"http": None, "https": None}
            )
            print(f"      ✅ 基础连接正常，状态码: {response.status_code}")
        except Exception as e:
            print(f"      ❌ 基础连接失败: {e}")
    
    print(f"\n📋 诊断建议:")
    print(f"• 如果测试1成功，说明需要禁用代理")
    print(f"• 如果测试2成功，说明需要使用代理")
    print(f"• 如果都失败，可能是网络或DNS问题")
    print(f"• 如果基础连接失败，可能是防火墙问题")

if __name__ == "__main__":
    test_domestic_apis()
