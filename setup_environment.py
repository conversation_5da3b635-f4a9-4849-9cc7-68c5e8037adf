#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
环境设置脚本

自动创建虚拟环境并安装依赖
"""

import os
import sys
import subprocess
import platform
from pathlib import Path


def run_command(command, description, check=True):
    """运行命令并显示进度"""
    print(f"🔄 {description}...")
    try:
        if isinstance(command, str):
            result = subprocess.run(command, shell=True, check=check, 
                                  capture_output=True, text=True)
        else:
            result = subprocess.run(command, check=check, 
                                  capture_output=True, text=True)
        
        if result.returncode == 0:
            print(f"✅ {description} 完成")
            return True
        else:
            print(f"❌ {description} 失败")
            if result.stderr:
                print(f"错误信息: {result.stderr}")
            return False
    except subprocess.CalledProcessError as e:
        print(f"❌ {description} 失败: {e}")
        if e.stderr:
            print(f"错误信息: {e.stderr}")
        return False
    except Exception as e:
        print(f"❌ {description} 失败: {e}")
        return False


def check_python_version():
    """检查Python版本"""
    print("🔍 检查Python版本...")
    version = sys.version_info
    if version.major == 3 and version.minor >= 8:
        print(f"✅ Python版本: {version.major}.{version.minor}.{version.micro}")
        return True
    else:
        print(f"❌ Python版本过低: {version.major}.{version.minor}.{version.micro}")
        print("需要Python 3.8或更高版本")
        return False


def create_virtual_environment():
    """创建虚拟环境"""
    venv_path = Path("venv")
    
    if venv_path.exists():
        print("📁 虚拟环境已存在，跳过创建")
        return True
    
    return run_command([sys.executable, "-m", "venv", "venv"], "创建虚拟环境")


def get_activation_command():
    """获取虚拟环境激活命令"""
    system = platform.system().lower()
    if system == "windows":
        return "venv\\Scripts\\activate"
    else:
        return "source venv/bin/activate"


def get_python_executable():
    """获取虚拟环境中的Python可执行文件路径"""
    system = platform.system().lower()
    if system == "windows":
        return Path("venv/Scripts/python.exe")
    else:
        return Path("venv/bin/python")


def install_dependencies():
    """安装项目依赖"""
    python_exe = get_python_executable()
    
    if not python_exe.exists():
        print("❌ 虚拟环境Python可执行文件不存在")
        return False
    
    # 升级pip
    success = run_command([str(python_exe), "-m", "pip", "install", "--upgrade", "pip"], 
                         "升级pip")
    if not success:
        return False
    
    # 安装主要依赖
    requirements_file = Path("requirements_minimal.txt")
    if requirements_file.exists():
        success = run_command([str(python_exe), "-m", "pip", "install", "-r",
                              str(requirements_file)], "安装核心依赖")
        if not success:
            print("⚠️ 核心依赖安装失败，尝试安装基础依赖...")
            # 尝试安装最基础的依赖
            basic_deps = ["requests", "aiohttp", "pillow", "numpy", "pyyaml"]
            for dep in basic_deps:
                run_command([str(python_exe), "-m", "pip", "install", dep],
                           f"安装 {dep}", check=False)
    else:
        print("⚠️ requirements_minimal.txt 不存在，安装基础依赖...")
        basic_deps = ["requests", "aiohttp", "pillow", "numpy", "pyyaml"]
        for dep in basic_deps:
            run_command([str(python_exe), "-m", "pip", "install", dep],
                       f"安装 {dep}", check=False)
    
    # 安装测试依赖
    test_requirements = Path("tests/requirements.txt")
    if test_requirements.exists():
        success = run_command([str(python_exe), "-m", "pip", "install", "-r", 
                              str(test_requirements)], "安装测试依赖")
        if not success:
            print("⚠️ 测试依赖安装失败，但不影响主程序运行")
    
    return True


def create_necessary_directories():
    """创建必要的目录"""
    directories = [
        "logs",
        "temp",
        "temp/cache",
        "temp/image_cache",
        "test_output",
        "coverage_report"
    ]
    
    print("📁 创建必要目录...")
    for directory in directories:
        Path(directory).mkdir(parents=True, exist_ok=True)
    print("✅ 目录创建完成")
    return True


def create_sample_config():
    """创建示例配置文件"""
    config_dir = Path("config")
    config_dir.mkdir(exist_ok=True)
    
    # 创建示例LLM配置
    llm_config_file = config_dir / "llm_config.json"
    if not llm_config_file.exists():
        sample_llm_config = {
            "models": [
                {
                    "name": "TestModel",
                    "type": "openai",
                    "key": "YOUR_API_KEY_HERE",
                    "url": "https://api.openai.com/v1"
                }
            ]
        }
        
        import json
        with open(llm_config_file, 'w', encoding='utf-8') as f:
            json.dump(sample_llm_config, f, indent=2, ensure_ascii=False)
        print("✅ 创建示例LLM配置文件")
    
    # 创建config.json目录和文件
    config_json_dir = config_dir / "config.json"
    config_json_dir.mkdir(exist_ok=True)
    
    # 应用配置
    app_config_file = config_json_dir / "app_config.json"
    if not app_config_file.exists():
        sample_app_config = {
            "app_settings": {
                "version": "2.0.0",
                "debug_mode": True,
                "default_style": "电影风格"
            }
        }
        
        with open(app_config_file, 'w', encoding='utf-8') as f:
            json.dump(sample_app_config, f, indent=2, ensure_ascii=False)
        print("✅ 创建示例应用配置文件")
    
    return True


def run_basic_tests():
    """运行基础测试"""
    python_exe = get_python_executable()
    
    print("🧪 运行基础测试...")
    
    # 测试导入
    test_imports = [
        "import sys; sys.path.append('src'); import core.service_manager; print('✅ 服务管理器导入成功')",
        "import sys; sys.path.append('src'); import utils.config_manager; print('✅ 配置管理器导入成功')",
        "import sys; sys.path.append('src'); import utils.logger; print('✅ 日志系统导入成功')",
        "import sys; sys.path.append('src'); import utils.error_handler; print('✅ 错误处理系统导入成功')"
    ]
    
    for test_import in test_imports:
        result = subprocess.run([str(python_exe), "-c", test_import], 
                              capture_output=True, text=True)
        if result.returncode == 0:
            print(result.stdout.strip())
        else:
            print(f"❌ 导入测试失败: {result.stderr}")
            return False
    
    return True


def create_run_script():
    """创建运行脚本"""
    system = platform.system().lower()
    
    if system == "windows":
        # Windows批处理文件
        run_script = """@echo off
echo 激活虚拟环境...
call venv\\Scripts\\activate
echo 运行AI视频生成器...
python main.py
pause
"""
        with open("run.bat", "w", encoding="utf-8") as f:
            f.write(run_script)
        print("✅ 创建Windows运行脚本: run.bat")
        
        # 测试脚本
        test_script = """@echo off
echo 激活虚拟环境...
call venv\\Scripts\\activate
echo 运行测试...
python run_tests.py --all
pause
"""
        with open("test.bat", "w", encoding="utf-8") as f:
            f.write(test_script)
        print("✅ 创建Windows测试脚本: test.bat")
    
    else:
        # Unix shell脚本
        run_script = """#!/bin/bash
echo "激活虚拟环境..."
source venv/bin/activate
echo "运行AI视频生成器..."
python main.py
"""
        with open("run.sh", "w") as f:
            f.write(run_script)
        os.chmod("run.sh", 0o755)
        print("✅ 创建Unix运行脚本: run.sh")
        
        # 测试脚本
        test_script = """#!/bin/bash
echo "激活虚拟环境..."
source venv/bin/activate
echo "运行测试..."
python run_tests.py --all
"""
        with open("test.sh", "w") as f:
            f.write(test_script)
        os.chmod("test.sh", 0o755)
        print("✅ 创建Unix测试脚本: test.sh")


def main():
    """主函数"""
    print("🚀 AI视频生成器环境设置")
    print("=" * 50)
    
    # 检查Python版本
    if not check_python_version():
        return False
    
    # 创建虚拟环境
    if not create_virtual_environment():
        return False
    
    # 安装依赖
    if not install_dependencies():
        return False
    
    # 创建必要目录
    if not create_necessary_directories():
        return False
    
    # 创建示例配置
    if not create_sample_config():
        return False
    
    # 运行基础测试
    if not run_basic_tests():
        return False
    
    # 创建运行脚本
    create_run_script()
    
    print("\n" + "=" * 50)
    print("🎉 环境设置完成！")
    print("\n📋 下一步操作:")
    
    activation_cmd = get_activation_command()
    system = platform.system().lower()
    
    if system == "windows":
        print("1. 激活虚拟环境:")
        print(f"   {activation_cmd}")
        print("\n2. 运行程序:")
        print("   python main.py")
        print("   或直接双击: run.bat")
        print("\n3. 运行测试:")
        print("   python run_tests.py --all")
        print("   或直接双击: test.bat")
    else:
        print("1. 激活虚拟环境:")
        print(f"   {activation_cmd}")
        print("\n2. 运行程序:")
        print("   python main.py")
        print("   或执行: ./run.sh")
        print("\n3. 运行测试:")
        print("   python run_tests.py --all")
        print("   或执行: ./test.sh")
    
    print("\n📁 重要目录:")
    print("   - logs/: 日志文件")
    print("   - test_output/: 测试报告")
    print("   - config/: 配置文件")
    print("   - temp/: 临时文件和缓存")
    
    print("\n🔧 配置提醒:")
    print("   请在 config/llm_config.json 中配置您的API密钥")
    
    return True


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
