#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试包初始化文件

提供测试基础设施和工具函数
"""

import os
import sys
import unittest
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))
sys.path.insert(0, str(project_root / "src"))

# 测试配置
TEST_CONFIG = {
    'test_data_dir': project_root / 'tests' / 'test_data',
    'temp_dir': project_root / 'tests' / 'temp',
    'mock_api_responses': True,
    'timeout': 30,
    'log_level': 'DEBUG'
}

# 确保测试目录存在
TEST_CONFIG['test_data_dir'].mkdir(parents=True, exist_ok=True)
TEST_CONFIG['temp_dir'].mkdir(parents=True, exist_ok=True)


class BaseTestCase(unittest.TestCase):
    """基础测试用例类"""
    
    @classmethod
    def setUpClass(cls):
        """类级别的设置"""
        cls.test_config = TEST_CONFIG
        cls.temp_dir = TEST_CONFIG['temp_dir']
        cls.test_data_dir = TEST_CONFIG['test_data_dir']
    
    def setUp(self):
        """每个测试方法的设置"""
        # 清理临时目录
        for file in self.temp_dir.glob('*'):
            if file.is_file():
                file.unlink()
    
    def tearDown(self):
        """每个测试方法的清理"""
        pass
    
    def assertFileExists(self, file_path):
        """断言文件存在"""
        self.assertTrue(Path(file_path).exists(), f"文件不存在: {file_path}")
    
    def assertFileNotExists(self, file_path):
        """断言文件不存在"""
        self.assertFalse(Path(file_path).exists(), f"文件不应该存在: {file_path}")
    
    def assertDictContainsSubset(self, subset, dictionary):
        """断言字典包含子集"""
        for key, value in subset.items():
            self.assertIn(key, dictionary)
            self.assertEqual(dictionary[key], value)


def create_test_suite():
    """创建测试套件"""
    loader = unittest.TestLoader()
    suite = unittest.TestSuite()
    
    # 发现并添加所有测试
    test_dir = Path(__file__).parent
    for test_file in test_dir.glob('test_*.py'):
        module_name = test_file.stem
        try:
            module = __import__(module_name)
            suite.addTests(loader.loadTestsFromModule(module))
        except ImportError as e:
            print(f"无法导入测试模块 {module_name}: {e}")
    
    return suite


def run_tests():
    """运行所有测试"""
    suite = create_test_suite()
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    return result.wasSuccessful()


if __name__ == '__main__':
    success = run_tests()
    sys.exit(0 if success else 1)
