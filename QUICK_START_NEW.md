# AI视频生成器 - 快速开始指南 🚀

## 📋 系统要求

- **操作系统**: Windows 10/11 (主要支持), Linux, macOS
- **Python版本**: 3.8 或更高版本
- **内存**: 建议 4GB 以上
- **存储空间**: 至少 2GB 可用空间
- **网络**: 稳定的互联网连接 (用于AI服务调用)

## ⚡ 一键安装 (推荐)

### Windows用户
```bash
# 1. 下载项目
git clone https://github.com/your-repo/AI_Video_Generator.git
cd AI_Video_Generator

# 2. 运行自动安装脚本
python setup_environment.py

# 3. 启动程序
start.bat
```

### Linux/macOS用户
```bash
# 1. 下载项目
git clone https://github.com/your-repo/AI_Video_Generator.git
cd AI_Video_Generator

# 2. 创建虚拟环境
python3 -m venv venv
source venv/bin/activate

# 3. 安装依赖
pip install -r requirements.txt

# 4. 启动程序
python main.py
```

## 🔧 手动安装

### 步骤1: 环境准备
```bash
# 检查Python版本
python --version  # 应该显示 3.8 或更高

# 创建项目目录
mkdir AI_Video_Generator
cd AI_Video_Generator

# 克隆项目
git clone https://github.com/your-repo/AI_Video_Generator.git .
```

### 步骤2: 虚拟环境
```bash
# 创建虚拟环境
python -m venv venv

# 激活虚拟环境
# Windows:
venv\Scripts\activate
# Linux/macOS:
source venv/bin/activate
```

### 步骤3: 安装依赖
```bash
# 方式1: 完整安装 (推荐)
pip install -r requirements.txt

# 方式2: 最小化安装 (快速体验)
pip install -r requirements_minimal.txt

# 方式3: 分步安装 (如果遇到问题)
pip install PyQt5 PyQtWebEngine
pip install aiohttp requests pillow numpy
pip install openai anthropic jieba
pip install tqdm colorama pyyaml
```

### 步骤4: 配置设置
```bash
# 复制配置模板
cp config/app_settings.example.json config/app_settings.json

# 编辑配置文件 (添加你的API密钥)
# Windows: notepad config/app_settings.json
# Linux/macOS: nano config/app_settings.json
```

### 步骤5: 启动程序
```bash
python main.py
```

## 🔑 API配置

### 必需配置
至少配置一个LLM服务提供商：

```json
{
    "models": [
        {
            "name": "DeepSeek",
            "type": "deepseek", 
            "key": "YOUR_DEEPSEEK_API_KEY",
            "url": "https://api.deepseek.com/v1/chat/completions"
        }
    ]
}
```

### 推荐配置
```json
{
    "models": [
        {
            "name": "DeepSeek",
            "type": "deepseek",
            "key": "YOUR_DEEPSEEK_API_KEY", 
            "url": "https://api.deepseek.com/v1/chat/completions"
        },
        {
            "name": "通义千问",
            "type": "tongyi",
            "key": "YOUR_TONGYI_API_KEY",
            "url": "https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions"
        }
    ],
    "image_generation": {
        "default_engine": "pollinations",
        "pollinations": {
            "enabled": true
        },
        "dalle": {
            "enabled": false,
            "api_key": "YOUR_OPENAI_API_KEY"
        }
    }
}
```

## 🎯 首次使用

### 1. 启动程序
运行 `python main.py` 或双击 `start.bat`

### 2. 创建项目
- 点击"新建项目"
- 输入项目名称
- 选择保存位置

### 3. 输入文本
在"文本处理"标签页输入你想要转换的文本内容

### 4. 生成分镜
- 切换到"五阶段分镜"标签页
- 选择LLM模型和风格
- 点击"开始第一阶段"

### 5. 生成图像
- 切换到"AI绘图"标签页
- 选择图像生成引擎
- 点击"批量生成"

## 🔍 功能测试

### 测试LLM服务
```python
# 运行简单测试
python simple_demo.py
```

### 测试图像生成
1. 在AI绘图标签页输入: "一只可爱的小猫"
2. 选择"Pollinations"引擎
3. 点击"生成图像"

### 测试完整流程
1. 输入短文本: "小明走进了一个神秘的森林"
2. 运行五阶段分镜生成
3. 批量生成图像
4. 查看输出结果

## ❗ 常见问题

### Q: 程序启动失败
**A**: 检查Python版本和依赖安装
```bash
python --version
pip list | grep PyQt5
```

### Q: 缺少模块错误
**A**: 重新安装依赖
```bash
pip install -r requirements.txt --force-reinstall
```

### Q: API调用失败
**A**: 检查配置文件和网络连接
- 确认API密钥正确
- 检查网络连接
- 查看日志文件: `logs/aivideologger.log`

### Q: 图像生成失败
**A**: 尝试不同的引擎
- Pollinations (免费，无需配置)
- 检查网络连接
- 查看错误日志

### Q: 内存不足
**A**: 优化设置
- 减少批量处理数量
- 关闭其他程序
- 使用最小化安装

## 🆘 获取帮助

### 日志查看
```bash
# 查看最新日志
tail -f logs/aivideologger.log

# Windows用户可以直接打开日志文件
notepad logs/aivideologger.log
```

### 调试模式
```bash
# 启用调试模式
set DEBUG=1
python main.py
```

### 联系支持
- 📧 邮箱: <EMAIL>
- 🐛 问题反馈: [GitHub Issues](https://github.com/your-repo/AI_Video_Generator/issues)
- 💬 讨论交流: [GitHub Discussions](https://github.com/your-repo/AI_Video_Generator/discussions)

## 🎉 成功启动标志

当你看到以下信息时，说明程序启动成功：
```
正在启动主程序...
[INFO] 服务管理器初始化完成
[INFO] 图像生成服务初始化完成
[INFO] 应用初始化完成
主程序窗口已显示
```

现在你可以开始使用AI视频生成器了！🎬
