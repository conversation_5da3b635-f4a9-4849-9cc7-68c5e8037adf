#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试修复后的Google API
"""

import os
import sys
import asyncio

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from src.services.llm_service import LLMService
from src.core.api_manager import APIManager
from src.utils.config_manager import ConfigManager

async def test_google_api_fixed():
    """测试修复后的Google API"""
    print("=" * 60)
    print("测试修复后的Google API")
    print("=" * 60)
    
    try:
        # 初始化服务
        print("🔧 初始化服务...")
        config_manager = ConfigManager("config")
        api_manager = APIManager(config_manager)
        await api_manager.initialize()
        llm_service = LLMService(api_manager)
        
        # 检查代理设置
        print("\n🔍 检查代理设置...")
        proxy_vars = ['HTTPS_PROXY', 'https_proxy', 'HTTP_PROXY', 'http_proxy']
        found_proxy = False
        for var in proxy_vars:
            value = os.environ.get(var)
            if value:
                print(f"   环境变量 {var}: {value}")
                found_proxy = True
        
        if not found_proxy:
            print("   未找到环境变量代理设置")
            print("   将尝试检测本地代理端口...")
            
            # 检测常见代理端口
            import socket
            proxy_ports = [7890, 7891, 1080, 8080, 10809]
            for port in proxy_ports:
                try:
                    sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
                    sock.settimeout(1)
                    result = sock.connect_ex(('127.0.0.1', port))
                    sock.close()
                    if result == 0:
                        print(f"   ✅ 检测到代理端口: 127.0.0.1:{port}")
                        break
                except:
                    continue
            else:
                print("   ❌ 未检测到活跃的代理端口")
        
        # 测试Google API
        print(f"\n🧪 测试Google Gemini API...")
        try:
            result = await llm_service.execute(
                provider="google",
                prompt="Hello, please reply with just 'Hi there!'",
                max_tokens=20,
                temperature=0.7
            )
            
            if result.success:
                print(f"✅ Google Gemini 成功: {result.data['content']}")
                print(f"   使用的提供商: {result.metadata.get('provider', '未知')}")
                print(f"   ✅ 代理设置修复成功！")
            else:
                print(f"❌ Google Gemini 失败: {result.error}")
                
                # 分析错误类型
                error_msg = result.error.lower()
                if "timeout" in error_msg:
                    print(f"   💡 建议: 可能是代理连接超时，请检查代理设置")
                elif "location" in error_msg or "region" in error_msg:
                    print(f"   💡 建议: 可能是地区限制，尝试切换代理节点")
                elif "key" in error_msg or "auth" in error_msg:
                    print(f"   💡 建议: 可能是API密钥问题")
                else:
                    print(f"   💡 建议: 检查网络连接和代理配置")
                    
        except Exception as e:
            print(f"❌ Google Gemini 异常: {e}")
        
        # 测试故障转移
        print(f"\n🔄 测试完整的故障转移机制...")
        result = await llm_service.execute_with_fallback(
            prompt="请用一句话介绍北京",
            max_tokens=50,
            temperature=0.7
        )
        
        if result.success:
            print(f"✅ 故障转移成功")
            print(f"   使用提供商: {result.metadata.get('provider', '未知')}")
            print(f"   响应内容: {result.data['content'][:80]}...")
        else:
            print(f"❌ 故障转移失败: {result.error}")
        
        print("\n" + "=" * 60)
        print("Google API修复测试完成")
        print("=" * 60)
        
    except Exception as e:
        print(f"❌ 测试过程中发生异常: {e}")
        import traceback
        print(f"详细错误: {traceback.format_exc()}")

if __name__ == "__main__":
    asyncio.run(test_google_api_fixed())
