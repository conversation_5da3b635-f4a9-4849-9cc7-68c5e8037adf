# 测试依赖包

# 核心测试框架
pytest>=7.0.0
pytest-cov>=4.0.0
pytest-mock>=3.10.0
pytest-asyncio>=0.21.0
pytest-xdist>=3.0.0

# 覆盖率分析
coverage>=7.0.0

# 性能测试
pytest-benchmark>=4.0.0

# 测试数据生成
factory-boy>=3.2.0
faker>=18.0.0

# HTTP测试
responses>=0.23.0
httpx>=0.24.0

# 异步测试支持
pytest-asyncio>=0.21.0
asynctest>=0.13.0

# 测试报告
pytest-html>=3.1.0
pytest-json-report>=1.5.0

# 代码质量检查
flake8>=6.0.0
black>=23.0.0
isort>=5.12.0
mypy>=1.0.0

# 安全检查
bandit>=1.7.0
safety>=2.3.0

# 文档测试
pytest-doctestplus>=0.12.0
