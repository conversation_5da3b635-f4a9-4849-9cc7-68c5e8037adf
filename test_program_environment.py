#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
在程序实际环境中测试LLM服务
"""

import os
import sys
import asyncio

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from src.services.llm_service import LLMService
from src.core.api_manager import APIManager
from src.utils.config_manager import ConfigManager

async def test_in_program_environment():
    """在程序环境中测试LLM服务"""
    print("=" * 60)
    print("程序环境LLM服务测试")
    print("=" * 60)
    
    try:
        # 初始化服务（模拟程序启动）
        print("🔧 初始化服务...")
        config_manager = ConfigManager("config")
        api_manager = APIManager(config_manager)
        await api_manager.initialize()
        llm_service = LLMService(api_manager)
        
        # 测试国内API
        domestic_providers = ['zhipu', 'tongyi', 'deepseek']
        
        for provider in domestic_providers:
            print(f"\n🧪 测试 {provider}...")
            
            try:
                start_time = asyncio.get_event_loop().time()
                
                # 使用与程序相同的调用方式
                result = await llm_service.execute(
                    provider=provider,
                    prompt="你好，请简单回复一句话",
                    max_tokens=50,
                    temperature=0.7
                )
                
                end_time = asyncio.get_event_loop().time()
                duration = end_time - start_time
                
                if result.success:
                    print(f"   ✅ 成功 ({duration:.2f}s): {result.data['content'][:50]}...")
                else:
                    print(f"   ❌ 失败: {result.error}")
                    
            except Exception as e:
                print(f"   ❌ 异常: {e}")
        
        # 测试故障转移
        print(f"\n🔄 测试故障转移机制...")
        try:
            start_time = asyncio.get_event_loop().time()
            
            result = await llm_service.execute_with_fallback(
                prompt="请用一句话介绍人工智能",
                max_tokens=100,
                temperature=0.7
            )
            
            end_time = asyncio.get_event_loop().time()
            duration = end_time - start_time
            
            if result.success:
                print(f"   ✅ 故障转移成功 ({duration:.2f}s)")
                print(f"   使用提供商: {result.metadata.get('provider', '未知')}")
                print(f"   响应内容: {result.data['content'][:80]}...")
            else:
                print(f"   ❌ 故障转移失败: {result.error}")
                
        except Exception as e:
            print(f"   ❌ 故障转移异常: {e}")
        
        # 测试并发调用
        print(f"\n🚀 测试并发调用...")
        try:
            tasks = []
            for i in range(3):
                task = llm_service.execute(
                    provider='zhipu',
                    prompt=f"这是第{i+1}个测试请求，请简单回复",
                    max_tokens=30,
                    temperature=0.7
                )
                tasks.append(task)
            
            start_time = asyncio.get_event_loop().time()
            results = await asyncio.gather(*tasks, return_exceptions=True)
            end_time = asyncio.get_event_loop().time()
            duration = end_time - start_time
            
            success_count = 0
            for i, result in enumerate(results):
                if isinstance(result, Exception):
                    print(f"   ❌ 请求{i+1}异常: {result}")
                elif result.success:
                    print(f"   ✅ 请求{i+1}成功: {result.data['content'][:30]}...")
                    success_count += 1
                else:
                    print(f"   ❌ 请求{i+1}失败: {result.error}")
            
            print(f"   📊 并发测试结果: {success_count}/3 成功，总耗时 {duration:.2f}s")
            
        except Exception as e:
            print(f"   ❌ 并发测试异常: {e}")
        
        print(f"\n📋 环境信息:")
        print(f"   Python版本: {sys.version}")
        print(f"   事件循环: {type(asyncio.get_event_loop()).__name__}")
        print(f"   可用提供商: {llm_service.get_available_providers()}")
        
    except Exception as e:
        print(f"❌ 测试过程中发生异常: {e}")
        import traceback
        print(f"详细错误: {traceback.format_exc()}")

if __name__ == "__main__":
    asyncio.run(test_in_program_environment())
