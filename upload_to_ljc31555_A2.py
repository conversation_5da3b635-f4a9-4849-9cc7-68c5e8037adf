#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI视频生成器 - 专为 ljc31555/A2 仓库定制的上传脚本
自动清理并重新上传整个项目
"""

import os
import subprocess
import sys
import shutil
from pathlib import Path

# 仓库配置
REPO_URL = "https://github.com/ljc31555/A2.git"
USERNAME = "ljc31555"
REPO_NAME = "A2"

def print_step(step_num, total_steps, description):
    """打印步骤信息"""
    print(f"\n[{step_num}/{total_steps}] {description}")
    print("-" * 50)

def run_command(command, description=""):
    """运行命令并处理结果"""
    print(f"执行: {command}")
    try:
        result = subprocess.run(command, shell=True, capture_output=True, text=True, encoding='utf-8')
        if result.stdout:
            print(f"输出: {result.stdout.strip()}")
        if result.stderr and result.returncode != 0:
            print(f"错误: {result.stderr.strip()}")
        return result.returncode == 0
    except Exception as e:
        print(f"命令执行异常: {e}")
        return False

def main():
    """主上传流程"""
    print("=" * 60)
    print("🎬 AI视频生成器 - 自动上传到 ljc31555/A2")
    print("=" * 60)
    print(f"🎯 目标仓库: {REPO_URL}")
    print(f"👤 GitHub用户: {USERNAME}")
    print()
    print("⚠️ 警告: 此操作将完全清理并重新上传GitHub仓库")
    print("⚠️ 远程仓库的所有现有内容将被替换")
    print()
    
    # 确认操作
    confirm = input("确认要继续吗? (y/N): ").strip().lower()
    if confirm not in ['y', 'yes']:
        print("❌ 操作已取消")
        return False
    
    print("\n🚀 开始自动上传流程...")
    
    # 步骤1: 清理Git历史
    print_step(1, 7, "清理现有Git历史")
    git_dir = Path(".git")
    if git_dir.exists():
        try:
            shutil.rmtree(git_dir)
            print("✅ 已删除现有Git历史")
        except Exception as e:
            print(f"⚠️ 删除Git历史失败: {e}")
    else:
        print("ℹ️ 没有现有Git历史")
    
    # 步骤2: 安全检查
    print_step(2, 7, "运行安全检查")
    if not run_command("python simple_security_check.py"):
        print("❌ 安全检查失败，请检查并修复问题")
        return False
    print("✅ 安全检查通过")
    
    # 步骤3: 清理项目
    print_step(3, 7, "清理项目文件")
    run_command("python cleanup.py")
    print("✅ 项目清理完成")
    
    # 步骤4: 初始化Git
    print_step(4, 7, "初始化Git仓库")
    if not run_command("git init"):
        print("❌ Git初始化失败")
        return False
    print("✅ Git仓库初始化完成")
    
    # 步骤5: 添加远程仓库
    print_step(5, 7, f"添加远程仓库: {REPO_URL}")
    if not run_command(f"git remote add origin {REPO_URL}"):
        print("❌ 添加远程仓库失败")
        return False
    print("✅ 远程仓库添加完成")
    
    # 步骤6: 提交文件
    print_step(6, 7, "添加和提交文件")
    
    # 添加所有文件
    if not run_command("git add ."):
        print("❌ 添加文件失败")
        return False
    
    # 检查是否有文件要提交
    result = subprocess.run("git status --porcelain", shell=True, capture_output=True, text=True)
    if not result.stdout.strip():
        print("ℹ️ 没有文件需要提交")
        return True
    
    # 提交文件
    commit_message = """🎬 AI视频生成器 - 完整项目重新上传

✨ 功能特性:
- 🎨 五阶段分镜生成系统 (世界观构建→场景分析→分镜生成→优化润色→最终输出)
- 🤖 多AI服务支持 (DeepSeek、通义千问、智谱AI、Google Gemini、OpenAI、SiliconFlow)
- 🖼️ 图像生成引擎 (Pollinations、ComfyUI、DALL-E、Stability AI、Google Imagen)
- 🎙️ 语音服务 (Azure TTS、ElevenLabs、OpenAI TTS、Edge TTS)
- 💻 现代化PyQt5界面 (响应式设计、主题支持)
- 📁 完整项目管理 (保存、加载、版本控制)
- ⚡ 异步处理架构 (非阻塞用户界面)
- 🔧 模块化设计 (清晰分层架构)

🔧 技术栈:
- Python 3.8+ (现代Python特性)
- PyQt5 5.15+ (跨平台GUI框架)
- 多种AI服务API集成
- 异步编程 (asyncio)
- 模块化架构设计
- 完善的错误处理和日志系统

📦 项目结构:
- src/ - 源代码 (核心层、服务层、处理器层、界面层)
- config/ - 配置文件模板 (安全的示例配置)
- tests/ - 完整测试套件
- docs/ - 详细文档和使用指南
- tools/ - 维护和部署工具

🔐 安全特性:
- ✅ 无真实API密钥泄露
- ✅ 完整的配置模板
- ✅ 敏感信息保护
- ✅ 安全检查通过

🚀 快速开始:
1. git clone https://github.com/ljc31555/A2.git
2. cd A2
3. python install.py
4. 配置API密钥
5. python main.py

📖 文档:
- README.md - 完整项目说明
- QUICK_START_NEW.md - 快速开始指南
- PROJECT_STRUCTURE.md - 项目结构说明
- GITHUB_UPLOAD_GUIDE.md - GitHub使用指南"""

    if not run_command(f'git commit -m "{commit_message}"'):
        print("❌ 提交失败")
        return False
    print("✅ 文件提交完成")
    
    # 步骤7: 强制推送
    print_step(7, 7, "强制推送到GitHub (清理远程仓库)")
    print("⚠️ 这将完全替换远程仓库的所有内容")
    
    # 强制推送到main分支
    if not run_command("git push -f origin main"):
        # 如果main分支失败，尝试master分支
        print("尝试推送到master分支...")
        if not run_command("git push -f origin master"):
            print("❌ 推送失败，可能需要身份验证")
            print("💡 如果需要身份验证，请提供GitHub Personal Access Token")
            return False
    
    print("✅ 强制推送完成 - 远程仓库已完全更新")
    
    # 显示完成信息
    print("\n" + "=" * 60)
    print("🎉 AI视频生成器已成功上传到GitHub!")
    print("=" * 60)
    print("📋 上传摘要:")
    print("✅ 远程仓库已完全清理")
    print("✅ 项目文件已重新上传")
    print("✅ 安全检查通过")
    print("✅ 敏感信息已保护")
    print()
    print("🔗 仓库链接:")
    print(f"   https://github.com/{USERNAME}/{REPO_NAME}")
    print()
    print("📖 下一步建议:")
    print("1. 访问GitHub仓库页面")
    print("2. 设置仓库描述:")
    print("   🎬 AI视频生成器 - 功能强大的AI驱动视频生成工具，支持五阶段分镜生成、多AI服务集成、现代化界面设计")
    print()
    print("3. 添加标签 (Topics):")
    print("   ai, video-generation, python, pyqt5, llm, image-generation, text-to-video, storyboard, artificial-intelligence, automation")
    print()
    print("4. 考虑创建Release版本 (v1.0.0)")
    print("5. 启用Issues和Discussions")
    print("=" * 60)
    
    return True

if __name__ == "__main__":
    try:
        success = main()
        if success:
            print("\n🎊 恭喜！您的AI视频生成器项目已成功发布到GitHub！")
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n❌ 操作被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 上传过程中发生错误: {e}")
        sys.exit(1)
