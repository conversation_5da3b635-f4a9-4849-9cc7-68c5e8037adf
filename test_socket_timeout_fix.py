#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试socket超时修复效果
"""

import os
import sys
import asyncio
import time

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from src.services.llm_service import LLMService
from src.core.api_manager import APIManager
from src.utils.config_manager import ConfigManager

async def test_socket_timeout_fix():
    """测试socket超时修复"""
    print("=" * 60)
    print("Socket超时修复测试")
    print("=" * 60)
    
    try:
        # 初始化服务
        print("🔧 初始化服务...")
        config_manager = ConfigManager("config")
        api_manager = APIManager(config_manager)
        await api_manager.initialize()
        llm_service = LLMService(api_manager)
        
        # 构造一个长提示词，模拟角色提取的情况
        long_prompt = """
        请分析以下文本，提取出所有角色信息，包括姓名、外貌描述、性格特点、身份地位等详细信息。
        
        文本内容：
        三国演义的故事背景设定在东汉末年，天下大乱，群雄并起的时代。

        主要角色包括：
        
        刘备：字玄德，蜀汉开国皇帝，仁德之君，身长七尺五寸，两耳垂肩，双手过膝，面如冠玉，唇若涂脂。性格仁慈宽厚，礼贤下士，有帝王之相。出身微寒，早年以织席贩履为业，后因黄巾起义而投军，逐渐崭露头角。他重视人才，善于团结人心，以仁德著称于世。在桃园三结义中与关羽、张飞结为异姓兄弟，共同创建蜀汉政权。
        
        关羽：字云长，刘备义弟，蜀汉五虎上将之首，身长九尺，髯长二尺，面如重枣，唇若涂脂，丹凤眼，卧蚕眉，相貌堂堂，威风凛凛。性格忠义无双，武艺高强，有万夫不当之勇。他忠心耿耿，义薄云天，被后世尊为"武圣"。关羽骄傲自负，看不起士大夫，但对刘备忠心不二，宁死不降。
        
        张飞：字翼德，刘备义弟，蜀汉五虎上将，身长八尺，豹头环眼，燕颔虎须，声若巨雷，势如奔马。性格粗犷豪爽，勇猛无敌，但有时鲁莽冲动。他出身富商，家境殷实，在桃园三结义中慷慨资助刘备起兵。张飞虽然外表粗犷，但内心细腻，善于识人，对有才能的人十分敬重。
        
        诸葛亮：字孔明，号卧龙，蜀汉丞相，身长八尺，面如冠玉，头戴纶巾，身披鹤氅，飘飘然有神仙之概。智谋过人，忠心耿耿，被誉为"智圣"。他出身名门，博学多才，精通天文地理、奇门遁甲、兵法战策。诸葛亮为人谨慎，做事周密，对蜀汉政权忠心不二，鞠躬尽瘁，死而后已。
        
        曹操：字孟德，小字阿瞒，魏国奠基者，身长七尺，细眼长髯，为人机变，好音乐，倜傥有雄才。性格多疑狡诈，雄才大略，既有政治家的远见，又有军事家的才能。他出身宦官家庭，但凭借自己的才能和手段，逐步统一北方，建立魏国政权。曹操善于用人，不拘一格，但同时也残酷无情，为达目的不择手段。
        
        孙权：字仲谋，东吴开国皇帝，紫髯碧眼，方颐大口，形貌奇伟。性格沉着冷静，善于用人，有帝王之度。他继承父兄基业，在江东建立稳固的政权，与曹操、刘备形成三足鼎立之势。孙权善于纳谏，能够听取臣下意见，同时也有决断力，在关键时刻能够做出正确决策。
        
        请按照以下格式输出角色信息：
        1. 角色姓名
        2. 字号别名
        3. 外貌描述
        4. 性格特点
        5. 身份地位
        6. 主要事迹
        
        要求详细分析每个角色的特点，不要遗漏任何重要信息。
        """
        
        print(f"📝 测试提示词长度: {len(long_prompt)} 字符")
        print(f"🕐 开始测试，预计需要较长时间...")
        
        # 测试智谱AI
        print(f"\n🧪 测试智谱AI（90秒socket超时）...")
        start_time = time.time()
        
        try:
            result = await llm_service.execute(
                provider="zhipu",
                prompt=long_prompt,
                max_tokens=3000,
                temperature=0.3
            )
            
            end_time = time.time()
            duration = end_time - start_time
            
            if result.success:
                print(f"✅ 智谱AI成功！")
                print(f"   耗时: {duration:.2f}秒")
                print(f"   响应长度: {len(result.data['content'])} 字符")
                print(f"   响应预览: {result.data['content'][:200]}...")
            else:
                print(f"❌ 智谱AI失败: {result.error}")
                print(f"   耗时: {duration:.2f}秒")
                
        except Exception as e:
            end_time = time.time()
            duration = end_time - start_time
            print(f"❌ 智谱AI异常: {e}")
            print(f"   耗时: {duration:.2f}秒")
        
        # 如果智谱AI失败，测试故障转移
        if not (hasattr(result, 'success') and result.success):
            print(f"\n🔄 测试故障转移机制...")
            start_time = time.time()
            
            try:
                result = await llm_service.execute_with_fallback(
                    prompt=long_prompt,
                    max_tokens=3000,
                    temperature=0.3
                )
                
                end_time = time.time()
                duration = end_time - start_time
                
                if result.success:
                    provider_used = result.metadata.get('provider', '未知')
                    print(f"✅ 故障转移成功！")
                    print(f"   使用提供商: {provider_used}")
                    print(f"   耗时: {duration:.2f}秒")
                    print(f"   响应长度: {len(result.data['content'])} 字符")
                    print(f"   响应预览: {result.data['content'][:200]}...")
                else:
                    print(f"❌ 故障转移失败: {result.error}")
                    print(f"   耗时: {duration:.2f}秒")
                    
            except Exception as e:
                end_time = time.time()
                duration = end_time - start_time
                print(f"❌ 故障转移异常: {e}")
                print(f"   耗时: {duration:.2f}秒")
        
        print(f"\n📋 修复总结:")
        print(f"• 总超时时间: 120秒")
        print(f"• Socket读取超时: 90秒（之前是30秒）")
        print(f"• 连接超时: 30秒")
        print(f"• 重试次数: 5次")
        print(f"• 如果测试成功，说明socket超时问题已解决")
        
    except Exception as e:
        print(f"❌ 测试过程中发生异常: {e}")
        import traceback
        print(f"详细错误: {traceback.format_exc()}")

if __name__ == "__main__":
    asyncio.run(test_socket_timeout_fix())
