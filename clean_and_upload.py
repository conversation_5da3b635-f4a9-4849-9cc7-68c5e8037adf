#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI视频生成器 - 清理并重新上传到GitHub
简化版自动上传脚本
"""

import os
import subprocess
import sys
import shutil
from pathlib import Path

def run_cmd(command):
    """运行命令"""
    print(f"执行: {command}")
    result = subprocess.run(command, shell=True, capture_output=True, text=True, encoding='utf-8')
    if result.stdout:
        print(result.stdout.strip())
    if result.stderr and result.returncode != 0:
        print(f"错误: {result.stderr.strip()}")
    return result.returncode == 0

def main():
    print("=" * 50)
    print("AI视频生成器 - 清理并重新上传")
    print("=" * 50)
    
    # 获取仓库URL
    repo_url = input("请输入GitHub仓库URL: ").strip()
    if not repo_url:
        print("错误: 仓库URL不能为空")
        return
    
    print(f"\n目标仓库: {repo_url}")
    confirm = input("确认要清理并重新上传吗? (y/N): ").strip().lower()
    if confirm not in ['y', 'yes']:
        print("操作已取消")
        return
    
    print("\n开始处理...")
    
    # 1. 清理Git历史
    print("\n[1/7] 清理Git历史...")
    if Path(".git").exists():
        shutil.rmtree(".git")
        print("✅ Git历史已清理")
    
    # 2. 安全检查
    print("\n[2/7] 安全检查...")
    if run_cmd("python simple_security_check.py"):
        print("✅ 安全检查通过")
    else:
        print("❌ 安全检查失败")
        return
    
    # 3. 清理项目
    print("\n[3/7] 清理项目...")
    run_cmd("python cleanup.py")
    print("✅ 项目清理完成")
    
    # 4. 初始化Git
    print("\n[4/7] 初始化Git...")
    if not run_cmd("git init"):
        print("❌ Git初始化失败")
        return
    
    # 5. 添加远程仓库
    print("\n[5/7] 添加远程仓库...")
    if not run_cmd(f"git remote add origin {repo_url}"):
        print("❌ 添加远程仓库失败")
        return
    
    # 6. 提交文件
    print("\n[6/7] 提交文件...")
    if not run_cmd("git add ."):
        print("❌ 添加文件失败")
        return
    
    commit_msg = "🎬 AI视频生成器 - 完整项目重新上传\\n\\n✨ 主要功能:\\n- 五阶段分镜生成系统\\n- 多AI服务支持\\n- 现代化PyQt5界面\\n- 完整项目管理\\n- 异步处理架构"
    
    if not run_cmd(f'git commit -m "{commit_msg}"'):
        print("❌ 提交失败")
        return
    
    # 7. 强制推送
    print("\n[7/7] 推送到GitHub...")
    if run_cmd("git push -f origin main") or run_cmd("git push -f origin master"):
        print("✅ 上传成功!")
        print(f"\n🎉 项目已成功上传到: {repo_url}")
        print("\n📋 建议设置:")
        print("- 仓库描述: 🎬 AI视频生成器 - 功能强大的AI驱动视频生成工具")
        print("- 标签: ai, video-generation, python, pyqt5, llm, image-generation")
    else:
        print("❌ 推送失败")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n操作被中断")
    except Exception as e:
        print(f"\n发生错误: {e}")
