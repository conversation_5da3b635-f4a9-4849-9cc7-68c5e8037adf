[tool:pytest]
# pytest配置文件

# 测试发现
testpaths = tests
python_files = test_*.py
python_classes = Test*
python_functions = test_*

# 输出配置
addopts = 
    -v
    --tb=short
    --strict-markers
    --disable-warnings
    --color=yes
    --durations=10
    --cov=src
    --cov-report=html:coverage_html
    --cov-report=xml:coverage.xml
    --cov-report=term-missing
    --cov-fail-under=80

# 标记定义
markers =
    unit: 单元测试
    integration: 集成测试
    slow: 慢速测试
    network: 需要网络连接的测试
    api: API测试
    gui: GUI测试
    performance: 性能测试

# 过滤警告
filterwarnings =
    ignore::DeprecationWarning
    ignore::PendingDeprecationWarning
    ignore::UserWarning:requests.*

# 最小版本要求
minversion = 6.0

# 测试目录
norecursedirs = 
    .git
    .tox
    dist
    build
    *.egg
    __pycache__
    .pytest_cache
    node_modules
