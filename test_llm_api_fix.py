#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试修复后的LLM API错误处理
"""

import os
import sys

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from src.models.llm_api import LLMApi

def test_llm_api_error_handling():
    """测试LLM API的错误处理"""
    print("测试LLM API错误处理...")
    
    # 测试智谱AI
    print("\n测试智谱AI:")
    zhipu_api = LLMApi(
        api_type="zhipu",
        api_key="ed7ffe9976bb4484ab16647564c0cb27.rI1BXVjDDDURMtJY",
        api_url="https://open.bigmodel.cn/api/paas/v4/chat/completions"
    )
    
    result = zhipu_api._make_api_call(
        "glm-4",
        [{"role": "user", "content": "你好"}],
        "测试调用"
    )
    print(f"智谱AI结果: {result}")
    
    # 测试Deepseek
    print("\n测试Deepseek:")
    deepseek_api = LLMApi(
        api_type="deepseek", 
        api_key="***********************************",
        api_url="https://api.deepseek.com/v1/chat/completions"
    )
    
    result = deepseek_api._make_api_call(
        "deepseek-chat",
        [{"role": "user", "content": "你好"}],
        "测试调用"
    )
    print(f"Deepseek结果: {result}")
    
    # 测试通义千问
    print("\n测试通义千问:")
    tongyi_api = LLMApi(
        api_type="tongyi",
        api_key="sk-ab30df729a9b4df287db20a8f47ba12c", 
        api_url="https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions"
    )
    
    result = tongyi_api._make_api_call(
        "qwen-turbo",
        [{"role": "user", "content": "你好"}],
        "测试调用"
    )
    print(f"通义千问结果: {result}")

if __name__ == "__main__":
    test_llm_api_error_handling()
