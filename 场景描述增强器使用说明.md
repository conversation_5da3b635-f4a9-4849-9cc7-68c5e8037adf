# 场景描述增强器使用说明

## 概述

场景描述增强器是AI视频生成器中的核心功能模块，用于智能优化和增强用户输入的场景描述，提高AI绘图和视频生成的质量和一致性。

## 何时使用场景描述增强器

### 适用场景

1. **场景描述过于简单**
   - 当您的场景描述只有几个词或一句话时
   - 例如："一个房间" → 增强后："一个温馨的现代客厅，柔和的暖光透过百叶窗洒在米色沙发上，茶几上放着一本翻开的书和一杯冒着热气的咖啡"

2. **需要技术细节优化**
   - 当您希望添加摄影技术参数时
   - 例如：添加景深、光线角度、构图方式等专业描述

3. **角色一致性要求**
   - 当场景中涉及特定角色，需要保持角色外观一致时
   - 系统会自动注入角色的详细描述信息

4. **批量场景处理**
   - 当您有多个相似场景需要统一风格时
   - 增强器可以保持描述风格的一致性

### 不适用场景

1. **描述已经非常详细**
   - 如果您的描述已经包含了丰富的细节，可能不需要额外增强

2. **追求极简风格**
   - 如果您希望保持简洁的描述风格

## 如何使用场景描述增强器

### 1. 打开配置面板

在五阶段分镜界面中：
1. 点击右上角的"场景增强器配置"按钮
2. 配置面板将以对话框形式打开

### 2. 基础配置

#### 融合策略
- **自然**：保持原始描述的自然语言风格
- **结构化**：按照固定模板结构化描述
- **简约**：在原描述基础上适度增强
- **智能**（推荐）：根据内容智能选择最佳策略

#### 质量阈值
- 范围：0.00 - 1.00
- 建议值：0.60
- 说明：低于此阈值的描述将被重新增强

#### 增强级别
- **低**：轻微增强，主要补充基础信息
- **中**（推荐）：适度增强，平衡详细度和可读性
- **高**：深度增强，添加丰富的技术和艺术细节

#### 功能开关
- **启用技术细节分析**：添加摄影和艺术技术参数
- **启用一致性注入**：自动注入角色和场景一致性信息
- **启用缓存机制**：缓存增强结果，提高处理速度

### 3. 高级配置

#### 性能模式
- **快速**：优先处理速度，可能牺牲一些质量
- **平衡**（推荐）：平衡速度和质量
- **质量**：优先质量，处理时间较长

#### 缓存设置
- **缓存大小限制**：10-1000 MB，建议100 MB
- **并发处理数**：1-8个，建议2个（根据电脑性能调整）

#### 自定义规则
可以通过JSON格式定义自定义增强规则：
```json
{
  "custom_patterns": [
    {
      "trigger": "室内",
      "enhancement": "温馨的室内环境，柔和的灯光"
    }
  ],
  "enhancement_rules": {
    "lighting": "always_add",
    "composition": "when_missing"
  },
  "quality_weights": {
    "detail_level": 0.7,
    "consistency": 0.9
  }
}
```

### 4. 测试预览

在配置完成后，建议进行测试：

1. **输入测试描述**
   - 在"测试描述"框中输入一个简单的场景描述
   - 例如："一个女孩在花园里"

2. **添加相关角色**
   - 如果场景涉及特定角色，在"相关角色"框中输入角色名称
   - 多个角色用逗号分隔

3. **运行测试**
   - 点击"运行测试"按钮
   - 系统将显示增强前后的对比结果

4. **查看结果**
   - 测试结果会显示在下方的文本框中
   - 包括原始描述、增强后描述、质量评分等信息

### 5. 性能监控

在"性能监控"标签页中可以查看：
- **平均处理时间**：每次增强的平均耗时
- **缓存命中率**：缓存使用效率
- **总处理次数**：累计处理的场景数量
- **平均质量评分**：增强结果的平均质量

## 使用后的结果查看

### 1. 实时查看

在五阶段分镜界面中：
- 当您输入场景描述后，如果启用了增强器
- 系统会自动在后台进行增强处理
- 增强后的描述会自动替换原始描述
- 您可以在场景描述输入框中看到增强后的内容

### 2. 详细结果查看

#### 在配置面板中
- 使用"测试预览"功能可以看到详细的增强过程
- 包括增强前后对比、质量评分、处理时间等

#### 在项目文件中
- 增强后的描述会保存在项目文件中
- 位置：`项目目录/五阶段分镜数据.json`
- 可以用文本编辑器查看完整的增强结果

### 3. 日志查看

#### 应用日志
- 在主界面点击"查看日志"可以看到增强器的运行日志
- 包括处理状态、错误信息、性能数据等

#### 性能日志
- 在配置面板的"性能监控"标签页中
- 可以查看详细的性能统计和历史记录

## 最佳实践建议

### 1. 配置建议

**新手用户**：
- 融合策略：智能
- 增强级别：中
- 质量阈值：0.60
- 性能模式：平衡

**高级用户**：
- 根据具体需求调整各项参数
- 使用自定义规则定制增强逻辑
- 定期查看性能监控优化配置

### 2. 使用技巧

1. **分阶段测试**
   - 先用简单描述测试效果
   - 确认配置合适后再处理复杂场景

2. **角色管理**
   - 确保角色信息在角色管理中已正确设置
   - 这样增强器才能正确注入角色一致性信息

3. **批量处理**
   - 对于大量相似场景，建议使用相同配置
   - 可以导出配置文件在不同项目间复用

4. **性能优化**
   - 根据电脑性能调整并发处理数
   - 定期清理缓存避免占用过多内存

### 3. 故障排除

**常见问题**：

1. **增强效果不明显**
   - 检查增强级别是否过低
   - 确认质量阈值设置是否合理

2. **处理速度慢**
   - 降低增强级别或切换到快速模式
   - 减少并发处理数

3. **结果不一致**
   - 检查角色信息是否完整
   - 确认一致性注入功能已启用

4. **内存占用高**
   - 降低缓存大小限制
   - 定期清空缓存

## 配置文件管理

### 导出配置
1. 在配置面板底部点击"导出配置"
2. 选择保存位置和文件名
3. 配置将以JSON格式保存

### 导入配置
1. 点击"导入配置"按钮
2. 选择之前导出的配置文件
3. 系统会自动应用导入的配置

### 配置文件格式
```json
{
  "config": {
    "fusion_strategy": "intelligent",
    "quality_threshold": 0.6,
    "enhancement_level": "medium",
    "enable_technical_details": true,
    "enable_consistency_injection": true,
    "cache_enabled": true,
    "performance_mode": "balanced"
  },
  "export_time": "2024-01-01T12:00:00",
  "version": "1.0"
}
```

## 总结

场景描述增强器是提升AI生成内容质量的重要工具。通过合理配置和使用，可以显著改善场景描述的丰富度和一致性，从而获得更好的AI绘图和视频生成效果。建议用户根据自己的需求和电脑性能，选择合适的配置参数，并通过测试功能验证效果。