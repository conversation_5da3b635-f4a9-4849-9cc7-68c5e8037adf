#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
服务管理器测试
"""

import unittest
import asyncio
from unittest.mock import Mock, patch, AsyncMock
from tests import BaseTestCase

from core.service_manager import ServiceManager, ServiceType, WorkflowStep
from core.service_base import ServiceResult


class TestServiceManager(BaseTestCase):
    """服务管理器测试类"""
    
    def setUp(self):
        """测试设置"""
        super().setUp()
        self.service_manager = ServiceManager()
    
    def tearDown(self):
        """测试清理"""
        super().tearDown()
        # 清理运行中的任务
        for task in self.service_manager.running_tasks.values():
            if not task.done():
                task.cancel()
    
    def test_service_manager_initialization(self):
        """测试服务管理器初始化"""
        self.assertIsNotNone(self.service_manager.api_manager)
        self.assertIsInstance(self.service_manager.services, dict)
        self.assertIsInstance(self.service_manager.workflows, dict)
        self.assertIsInstance(self.service_manager.running_tasks, dict)
    
    def test_service_registration(self):
        """测试服务注册"""
        # 检查默认服务是否已注册
        self.assertIn(ServiceType.LLM, self.service_manager.services)
        self.assertIn(ServiceType.IMAGE, self.service_manager.services)
        self.assertIn(ServiceType.VOICE, self.service_manager.services)
    
    def test_workflow_creation(self):
        """测试工作流创建"""
        workflow_name = self.service_manager.create_video_generation_workflow(
            text="测试文本",
            style="测试风格"
        )
        
        self.assertIsInstance(workflow_name, str)
        self.assertIn(workflow_name, self.service_manager.workflows)
        
        workflow = self.service_manager.workflows[workflow_name]
        self.assertIsInstance(workflow, list)
        self.assertTrue(len(workflow) > 0)
        
        # 检查工作流步骤
        for step in workflow:
            self.assertIsInstance(step, WorkflowStep)
            self.assertIn(step.service_type, [ServiceType.LLM, ServiceType.IMAGE, ServiceType.VOICE])
    
    @patch('core.service_manager.ServiceManager.execute_service_method')
    async def test_execute_service_method(self, mock_execute):
        """测试服务方法执行"""
        # 模拟成功响应
        mock_execute.return_value = ServiceResult(
            success=True,
            data="测试数据",
            message="执行成功"
        )
        
        result = await self.service_manager.execute_service_method(
            ServiceType.LLM,
            "generate_storyboard",
            text="测试文本"
        )
        
        self.assertIsInstance(result, ServiceResult)
        self.assertTrue(result.success)
        self.assertEqual(result.data, "测试数据")
        mock_execute.assert_called_once()
    
    async def test_workflow_execution(self):
        """测试工作流执行"""
        # 创建简单的测试工作流
        workflow_name = "test_workflow"
        steps = [
            WorkflowStep(
                service_type=ServiceType.LLM,
                method="generate_storyboard",
                params={"text": "测试文本"},
                step_id="step1"
            )
        ]
        
        self.service_manager.register_workflow(workflow_name, steps)
        
        # 模拟服务执行
        with patch.object(self.service_manager, 'execute_service_method') as mock_execute:
            mock_execute.return_value = ServiceResult(
                success=True,
                data="测试结果"
            )
            
            results = await self.service_manager.execute_workflow(workflow_name)
            
            self.assertIsInstance(results, dict)
            self.assertIn("step1", results)
            self.assertTrue(results["step1"].success)
    
    async def test_workflow_with_dependencies(self):
        """测试带依赖的工作流"""
        workflow_name = "test_dependency_workflow"
        steps = [
            WorkflowStep(
                service_type=ServiceType.LLM,
                method="generate_storyboard",
                params={"text": "测试文本"},
                step_id="step1"
            ),
            WorkflowStep(
                service_type=ServiceType.IMAGE,
                method="generate_image",
                params={"prompt": "测试提示"},
                step_id="step2",
                depends_on=["step1"]
            )
        ]
        
        self.service_manager.register_workflow(workflow_name, steps)
        
        # 模拟服务执行
        with patch.object(self.service_manager, 'execute_service_method') as mock_execute:
            mock_execute.return_value = ServiceResult(
                success=True,
                data="测试结果"
            )
            
            results = await self.service_manager.execute_workflow(workflow_name)
            
            self.assertIsInstance(results, dict)
            self.assertEqual(len(results), 2)
            self.assertIn("step1", results)
            self.assertIn("step2", results)
    
    def test_workflow_step_creation(self):
        """测试工作流步骤创建"""
        step = WorkflowStep(
            service_type=ServiceType.LLM,
            method="test_method",
            params={"param1": "value1"}
        )
        
        self.assertEqual(step.service_type, ServiceType.LLM)
        self.assertEqual(step.method, "test_method")
        self.assertEqual(step.params, {"param1": "value1"})
        self.assertEqual(step.depends_on, [])
        self.assertTrue(step.step_id.startswith("LLM_test_method"))
    
    def test_workflow_step_with_dependencies(self):
        """测试带依赖的工作流步骤"""
        step = WorkflowStep(
            service_type=ServiceType.IMAGE,
            method="test_method",
            params={"param1": "value1"},
            depends_on=["step1", "step2"],
            step_id="custom_step"
        )
        
        self.assertEqual(step.depends_on, ["step1", "step2"])
        self.assertEqual(step.step_id, "custom_step")
    
    async def test_convenience_methods(self):
        """测试便捷方法"""
        with patch.object(self.service_manager, 'execute_service_method') as mock_execute:
            mock_execute.return_value = ServiceResult(success=True, data="测试结果")
            
            # 测试生成分镜
            result = await self.service_manager.generate_storyboard("测试文本")
            self.assertTrue(result.success)
            
            # 测试生成图像
            result = await self.service_manager.generate_image("测试提示")
            self.assertTrue(result.success)
            
            # 测试文本转语音
            result = await self.service_manager.text_to_speech("测试文本")
            self.assertTrue(result.success)
    
    def test_service_status_monitoring(self):
        """测试服务状态监控"""
        status = self.service_manager.get_service_status()
        
        self.assertIsInstance(status, dict)
        self.assertIn('services', status)
        self.assertIn('workflows', status)
        self.assertIn('running_tasks', status)
        
        # 检查服务状态
        for service_type in ServiceType:
            if service_type in self.service_manager.services:
                service_status = status['services'][service_type.value]
                self.assertIn('status', service_status)
                self.assertIn('request_count', service_status)
    
    async def test_async_task_execution(self):
        """测试异步任务执行"""
        task_name = "test_task"
        
        async def test_coroutine():
            await asyncio.sleep(0.1)
            return "任务完成"
        
        # 启动异步任务
        task = self.service_manager.start_async_task(task_name, test_coroutine())
        
        self.assertIn(task_name, self.service_manager.running_tasks)
        
        # 等待任务完成
        result = await task
        self.assertEqual(result, "任务完成")
        
        # 检查任务是否已清理
        self.assertNotIn(task_name, self.service_manager.running_tasks)
    
    def test_error_handling(self):
        """测试错误处理"""
        # 测试不存在的工作流
        with self.assertRaises(ValueError):
            asyncio.run(self.service_manager.execute_workflow("不存在的工作流"))
    
    def test_dependency_graph_building(self):
        """测试依赖图构建"""
        steps = [
            WorkflowStep(
                service_type=ServiceType.LLM,
                method="method1",
                params={},
                step_id="step1"
            ),
            WorkflowStep(
                service_type=ServiceType.IMAGE,
                method="method2",
                params={},
                step_id="step2",
                depends_on=["step1"]
            )
        ]
        
        graph = self.service_manager._build_dependency_graph(steps)
        
        self.assertIsInstance(graph, dict)
        self.assertEqual(graph["step1"], [])
        self.assertEqual(graph["step2"], ["step1"])


def run_async_test(coro):
    """运行异步测试的辅助函数"""
    loop = asyncio.new_event_loop()
    asyncio.set_event_loop(loop)
    try:
        return loop.run_until_complete(coro)
    finally:
        loop.close()


if __name__ == '__main__':
    unittest.main()
