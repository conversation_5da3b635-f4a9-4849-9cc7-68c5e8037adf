#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试项目删除功能
"""

import os
import sys
import json
import shutil
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from src.core.project_manager_adapter import ProjectManagerAdapter

def test_project_delete():
    """测试项目删除功能"""
    print("开始测试项目删除功能...")
    
    # 初始化项目管理器
    config_dir = "config"
    pm = ProjectManagerAdapter(config_dir)
    
    # 创建测试项目
    test_project_name = "测试删除项目"
    print(f"创建测试项目: {test_project_name}")
    
    try:
        # 创建项目
        success = pm.create_new_project(test_project_name, "这是一个用于测试删除功能的项目")
        if not success:
            print("❌ 创建测试项目失败")
            return False
        
        print("✅ 测试项目创建成功")
        
        # 列出项目，确认项目存在
        projects = pm.list_projects()
        test_project = None
        for project in projects:
            if project["name"] == test_project_name:
                test_project = project
                break
        
        if not test_project:
            print("❌ 在项目列表中找不到测试项目")
            return False
        
        print(f"✅ 找到测试项目，路径: {test_project['path']}")
        
        # 验证路径是目录而不是文件
        project_path = test_project['path']
        if not os.path.isdir(project_path):
            print(f"❌ 项目路径不是目录: {project_path}")
            return False
        
        print("✅ 项目路径验证正确（是目录）")
        
        # 尝试删除项目
        print("开始删除项目...")
        try:
            shutil.rmtree(project_path)
            print("✅ 项目删除成功")
            
            # 验证项目确实被删除
            if os.path.exists(project_path):
                print("❌ 项目目录仍然存在")
                return False
            
            print("✅ 项目目录已被删除")
            return True
            
        except Exception as e:
            print(f"❌ 删除项目失败: {e}")
            return False
            
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        return False

def test_list_projects_path_format():
    """测试项目列表返回的路径格式"""
    print("\n开始测试项目列表路径格式...")
    
    config_dir = "config"
    pm = ProjectManagerAdapter(config_dir)
    
    # 列出所有项目
    projects = pm.list_projects()
    
    print(f"找到 {len(projects)} 个项目:")
    for i, project in enumerate(projects, 1):
        path = project['path']
        name = project['name']
        
        print(f"{i}. 项目名: {name}")
        print(f"   路径: {path}")
        print(f"   是目录: {os.path.isdir(path)}")
        print(f"   是文件: {os.path.isfile(path)}")
        
        if os.path.isfile(path):
            print(f"   ❌ 警告: 路径指向文件而不是目录!")
        elif os.path.isdir(path):
            print(f"   ✅ 路径格式正确")
        else:
            print(f"   ❌ 路径不存在")
        print()

if __name__ == "__main__":
    print("=" * 50)
    print("项目删除功能测试")
    print("=" * 50)
    
    # 测试项目列表路径格式
    test_list_projects_path_format()
    
    # 测试项目删除功能
    success = test_project_delete()
    
    print("\n" + "=" * 50)
    if success:
        print("✅ 所有测试通过！项目删除功能已修复。")
    else:
        print("❌ 测试失败！项目删除功能仍有问题。")
    print("=" * 50)
