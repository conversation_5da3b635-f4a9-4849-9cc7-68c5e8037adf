#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单测试Google API连接
"""

import requests
import json

def test_google_api_simple():
    """简单测试Google API"""
    print("=" * 50)
    print("Google API简单连接测试")
    print("=" * 50)
    
    # 从配置文件读取Google API配置
    try:
        with open('config/llm_config.json', 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        google_config = None
        for model in config['models']:
            if model['type'] == 'google':
                google_config = model
                break
        
        if not google_config:
            print("❌ 未找到Google API配置")
            return
        
        api_key = google_config['key']
        base_url = google_config['url']
        
        print(f"API Key: {api_key[:20]}...")
        print(f"Base URL: {base_url}")
        
        # 构造完整URL
        url = f"{base_url}?key={api_key}"
        print(f"完整URL: {url[:100]}...")
        
        # 测试数据
        data = {
            "contents": [{
                "parts": [{"text": "Hello, please say hi"}]
            }],
            "generationConfig": {
                "maxOutputTokens": 50,
                "temperature": 0.7
            }
        }
        
        headers = {
            'Content-Type': 'application/json',
            'User-Agent': 'AI-Video-Generator/1.0'
        }
        
        print("\n🧪 测试1: 使用系统代理...")
        try:
            response = requests.post(
                url,
                json=data,
                headers=headers,
                timeout=60
                # 使用系统代理设置
            )
            
            print(f"状态码: {response.status_code}")
            if response.status_code == 200:
                result = response.json()
                content = result['candidates'][0]['content']['parts'][0]['text']
                print(f"✅ 成功: {content}")
            else:
                print(f"❌ 失败: {response.text}")
                
        except requests.exceptions.Timeout:
            print("❌ 请求超时")
        except requests.exceptions.ConnectionError as e:
            print(f"❌ 连接错误: {e}")
        except Exception as e:
            print(f"❌ 其他错误: {e}")
        
        print("\n🧪 测试2: 禁用代理...")
        try:
            response = requests.post(
                url,
                json=data,
                headers=headers,
                timeout=30,
                proxies={"http": None, "https": None}
            )
            
            print(f"状态码: {response.status_code}")
            if response.status_code == 200:
                result = response.json()
                content = result['candidates'][0]['content']['parts'][0]['text']
                print(f"✅ 成功: {content}")
            else:
                print(f"❌ 失败: {response.text}")
                
        except requests.exceptions.Timeout:
            print("❌ 请求超时")
        except requests.exceptions.ConnectionError as e:
            print(f"❌ 连接错误: {e}")
        except Exception as e:
            print(f"❌ 其他错误: {e}")
        
        print("\n📋 结论:")
        print("• 如果测试1成功，说明需要使用系统代理")
        print("• 如果测试2成功，说明不需要代理")
        print("• 如果都失败，可能是API密钥或地区限制问题")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")

if __name__ == "__main__":
    test_google_api_simple()
