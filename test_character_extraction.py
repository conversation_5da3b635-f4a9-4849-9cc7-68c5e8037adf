#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试角色提取功能的超时设置
"""

import os
import sys
import asyncio
import time

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from src.services.llm_service import LLMService
from src.core.api_manager import APIManager
from src.utils.config_manager import ConfigManager
from src.utils.character_scene_manager import CharacterSceneManager
from src.core.service_manager import ServiceManager

async def test_character_extraction():
    """测试角色提取功能"""
    print("=" * 60)
    print("角色提取功能超时测试")
    print("=" * 60)
    
    try:
        # 初始化服务
        print("🔧 初始化服务...")
        config_manager = ConfigManager("config")
        api_manager = APIManager(config_manager)
        await api_manager.initialize()
        
        # 检查超时设置
        print("\n📊 检查API超时配置:")
        from src.core.api_manager import APIType
        for api_type in ['zhipu', 'tongyi', 'deepseek', 'google']:
            api_config = api_manager.get_best_api(APIType.LLM, api_type)
            if api_config:
                print(f"   {api_config.name}: {api_config.timeout}秒")
        
        # 初始化服务管理器
        service_manager = ServiceManager()
        service_manager.api_manager = api_manager
        await service_manager.initialize()
        
        # 初始化角色场景管理器
        character_manager = CharacterSceneManager(
            project_root="test_project",
            service_manager=service_manager
        )
        
        # 测试文本（模拟世界观圣经内容）
        test_text = """
        三国演义的故事背景设定在东汉末年，天下大乱，群雄并起的时代。

        主要角色包括：
        
        刘备：字玄德，蜀汉开国皇帝，仁德之君，身长七尺五寸，两耳垂肩，双手过膝，面如冠玉，唇若涂脂。性格仁慈宽厚，礼贤下士，有帝王之相。
        
        关羽：字云长，刘备义弟，蜀汉五虎上将之首，身长九尺，髯长二尺，面如重枣，唇若涂脂，丹凤眼，卧蚕眉，相貌堂堂，威风凛凛。性格忠义无双，武艺高强。
        
        张飞：字翼德，刘备义弟，蜀汉五虎上将，身长八尺，豹头环眼，燕颔虎须，声若巨雷，势如奔马。性格粗犷豪爽，勇猛无敌。
        
        诸葛亮：字孔明，蜀汉丞相，身长八尺，面如冠玉，头戴纶巾，身披鹤氅，飘飘然有神仙之概。智谋过人，忠心耿耿。
        
        曹操：字孟德，魏国奠基者，身长七尺，细眼长髯，为人机变，好音乐，倜傥有雄才。性格多疑狡诈，雄才大略。
        
        孙权：字仲谋，东吴开国皇帝，紫髯碧眼，方颐大口，形貌奇伟。性格沉着冷静，善于用人。
        
        故事发生在洛阳、长安、荆州、益州、江东等地，涉及宫廷、军营、茅庐、战场等多种场景。
        """
        
        print(f"\n🧪 开始角色提取测试...")
        print(f"   测试文本长度: {len(test_text)} 字符")
        
        start_time = time.time()
        
        try:
            # 执行角色提取
            characters = character_manager.extract_characters_from_text(
                text=test_text,
                world_bible="这是三国演义的世界观设定，发生在东汉末年的中国。"
            )
            
            end_time = time.time()
            duration = end_time - start_time
            
            print(f"\n✅ 角色提取成功！")
            print(f"   耗时: {duration:.2f}秒")
            print(f"   提取到 {len(characters)} 个角色:")
            
            for i, char in enumerate(characters, 1):
                name = char.get('name', '未知')
                description = char.get('description', '无描述')
                print(f"   {i}. {name}: {description[:50]}...")
                
        except Exception as e:
            end_time = time.time()
            duration = end_time - start_time
            print(f"\n❌ 角色提取失败！")
            print(f"   耗时: {duration:.2f}秒")
            print(f"   错误: {e}")
        
        # 测试简单的LLM调用作为对比
        print(f"\n🔄 对比测试：简单LLM调用...")
        
        llm_service = service_manager.get_service(service_manager.ServiceType.LLM)
        
        start_time = time.time()
        try:
            result = await llm_service.execute_with_fallback(
                prompt="请简单介绍三国演义的主要角色，限制在100字以内",
                max_tokens=200,
                temperature=0.7
            )
            
            end_time = time.time()
            duration = end_time - start_time
            
            if result.success:
                print(f"✅ 简单调用成功！")
                print(f"   耗时: {duration:.2f}秒")
                print(f"   使用提供商: {result.metadata.get('provider', '未知')}")
                print(f"   响应: {result.data['content'][:100]}...")
            else:
                print(f"❌ 简单调用失败: {result.error}")
                
        except Exception as e:
            end_time = time.time()
            duration = end_time - start_time
            print(f"❌ 简单调用异常: {e}")
            print(f"   耗时: {duration:.2f}秒")
        
        print(f"\n📋 测试总结:")
        print(f"• 配置超时时间: 120秒")
        print(f"• 线程池超时: 180秒")
        print(f"• 如果角色提取成功，说明超时问题已解决")
        print(f"• 如果仍然失败，可能需要进一步优化网络设置")
        
    except Exception as e:
        print(f"❌ 测试过程中发生异常: {e}")
        import traceback
        print(f"详细错误: {traceback.format_exc()}")

if __name__ == "__main__":
    asyncio.run(test_character_extraction())
