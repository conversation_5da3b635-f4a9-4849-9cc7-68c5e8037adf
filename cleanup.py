#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI视频生成器 - 项目清理脚本
清理临时文件、缓存文件和无用文件
"""

import os
import shutil
import glob
from pathlib import Path

def print_banner():
    """打印清理横幅"""
    print("=" * 60)
    print("🧹 AI视频生成器 - 项目清理工具")
    print("=" * 60)

def clean_python_cache():
    """清理Python缓存文件"""
    print("🗑️ 清理Python缓存文件...")
    
    cache_dirs = []
    pyc_files = []
    
    # 查找__pycache__目录
    for root, dirs, files in os.walk("."):
        if "__pycache__" in dirs:
            cache_dirs.append(os.path.join(root, "__pycache__"))
        
        # 查找.pyc文件
        for file in files:
            if file.endswith(('.pyc', '.pyo')):
                pyc_files.append(os.path.join(root, file))
    
    # 删除缓存目录
    for cache_dir in cache_dirs:
        try:
            shutil.rmtree(cache_dir)
            print(f"   ✅ 删除缓存目录: {cache_dir}")
        except Exception as e:
            print(f"   ❌ 删除失败: {cache_dir} - {e}")
    
    # 删除.pyc文件
    for pyc_file in pyc_files:
        try:
            os.remove(pyc_file)
            print(f"   ✅ 删除缓存文件: {pyc_file}")
        except Exception as e:
            print(f"   ❌ 删除失败: {pyc_file} - {e}")
    
    print(f"   📊 清理完成: {len(cache_dirs)} 个目录, {len(pyc_files)} 个文件")

def clean_logs(keep_latest=True):
    """清理日志文件"""
    print("📝 清理日志文件...")
    
    log_dir = Path("logs")
    if not log_dir.exists():
        print("   ℹ️ 日志目录不存在")
        return
    
    log_files = list(log_dir.glob("*.log"))
    
    if not log_files:
        print("   ℹ️ 没有找到日志文件")
        return
    
    if keep_latest and len(log_files) > 1:
        # 保留最新的日志文件
        log_files.sort(key=lambda x: x.stat().st_mtime, reverse=True)
        files_to_delete = log_files[1:]  # 删除除最新外的所有文件
        
        for log_file in files_to_delete:
            try:
                log_file.unlink()
                print(f"   ✅ 删除旧日志: {log_file}")
            except Exception as e:
                print(f"   ❌ 删除失败: {log_file} - {e}")
        
        print(f"   📊 保留最新日志: {log_files[0]}")
    else:
        print("   ℹ️ 保留所有日志文件")

def clean_temp_files():
    """清理临时文件"""
    print("🗂️ 清理临时文件...")
    
    temp_patterns = [
        "*.tmp",
        "*.bak", 
        "*.old",
        "*.swp",
        "*.swo",
        "*~"
    ]
    
    deleted_count = 0
    for pattern in temp_patterns:
        for file_path in glob.glob(pattern, recursive=True):
            try:
                os.remove(file_path)
                print(f"   ✅ 删除临时文件: {file_path}")
                deleted_count += 1
            except Exception as e:
                print(f"   ❌ 删除失败: {file_path} - {e}")
    
    print(f"   📊 清理完成: {deleted_count} 个临时文件")

def clean_image_cache(keep_recent_days=7):
    """清理图像缓存"""
    print("🖼️ 清理图像缓存...")
    
    cache_dir = Path("temp/image_cache")
    if not cache_dir.exists():
        print("   ℹ️ 图像缓存目录不存在")
        return
    
    import time
    current_time = time.time()
    cutoff_time = current_time - (keep_recent_days * 24 * 60 * 60)
    
    deleted_count = 0
    total_size = 0
    
    for image_file in cache_dir.rglob("*"):
        if image_file.is_file():
            try:
                file_time = image_file.stat().st_mtime
                if file_time < cutoff_time:
                    file_size = image_file.stat().st_size
                    image_file.unlink()
                    deleted_count += 1
                    total_size += file_size
                    print(f"   ✅ 删除过期缓存: {image_file}")
            except Exception as e:
                print(f"   ❌ 删除失败: {image_file} - {e}")
    
    # 删除空目录
    for dir_path in cache_dir.rglob("*"):
        if dir_path.is_dir() and not any(dir_path.iterdir()):
            try:
                dir_path.rmdir()
                print(f"   ✅ 删除空目录: {dir_path}")
            except Exception as e:
                print(f"   ❌ 删除失败: {dir_path} - {e}")
    
    size_mb = total_size / (1024 * 1024)
    print(f"   📊 清理完成: {deleted_count} 个文件, {size_mb:.2f} MB")

def clean_output_temp():
    """清理输出目录中的临时文件"""
    print("📁 清理输出临时文件...")
    
    output_dir = Path("output")
    if not output_dir.exists():
        print("   ℹ️ 输出目录不存在")
        return
    
    temp_patterns = ["*.tmp", "*.part", "*.downloading"]
    deleted_count = 0
    
    for pattern in temp_patterns:
        for temp_file in output_dir.rglob(pattern):
            try:
                temp_file.unlink()
                print(f"   ✅ 删除临时文件: {temp_file}")
                deleted_count += 1
            except Exception as e:
                print(f"   ❌ 删除失败: {temp_file} - {e}")
    
    print(f"   📊 清理完成: {deleted_count} 个临时文件")

def clean_test_files():
    """清理测试文件"""
    print("🧪 清理测试文件...")
    
    test_patterns = [
        ".pytest_cache",
        ".coverage",
        "htmlcov",
        "*.coverage"
    ]
    
    deleted_count = 0
    for pattern in test_patterns:
        for item in glob.glob(pattern, recursive=True):
            try:
                if os.path.isdir(item):
                    shutil.rmtree(item)
                    print(f"   ✅ 删除测试目录: {item}")
                else:
                    os.remove(item)
                    print(f"   ✅ 删除测试文件: {item}")
                deleted_count += 1
            except Exception as e:
                print(f"   ❌ 删除失败: {item} - {e}")
    
    print(f"   📊 清理完成: {deleted_count} 个测试文件")

def get_directory_size(path):
    """获取目录大小"""
    total_size = 0
    for dirpath, dirnames, filenames in os.walk(path):
        for filename in filenames:
            filepath = os.path.join(dirpath, filename)
            try:
                total_size += os.path.getsize(filepath)
            except (OSError, FileNotFoundError):
                pass
    return total_size

def show_cleanup_summary():
    """显示清理摘要"""
    print("\n" + "=" * 60)
    print("📊 清理摘要")
    print("=" * 60)
    
    # 检查各目录大小
    directories = {
        "logs": "logs",
        "temp": "temp", 
        "output": "output",
        "venv": "venv"
    }
    
    for name, path in directories.items():
        if os.path.exists(path):
            size = get_directory_size(path)
            size_mb = size / (1024 * 1024)
            print(f"{name:10}: {size_mb:8.2f} MB")
        else:
            print(f"{name:10}: {'不存在':>8}")

def main():
    """主清理流程"""
    print_banner()
    
    print("开始清理项目文件...")
    print()
    
    # 执行各种清理操作
    clean_python_cache()
    print()
    
    clean_logs(keep_latest=True)
    print()
    
    clean_temp_files()
    print()
    
    clean_image_cache(keep_recent_days=7)
    print()
    
    clean_output_temp()
    print()
    
    clean_test_files()
    print()
    
    show_cleanup_summary()
    
    print("\n" + "=" * 60)
    print("🎉 项目清理完成！")
    print("=" * 60)
    print("📋 清理内容:")
    print("✅ Python缓存文件 (__pycache__, *.pyc)")
    print("✅ 旧日志文件 (保留最新)")
    print("✅ 临时文件 (*.tmp, *.bak, *.old)")
    print("✅ 过期图像缓存 (7天前)")
    print("✅ 输出临时文件")
    print("✅ 测试缓存文件")
    print("\n💡 提示:")
    print("- 重要文件已保留")
    print("- 可以随时重新生成缓存")
    print("- 建议定期运行此脚本")
    print("=" * 60)

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n❌ 清理被用户中断")
    except Exception as e:
        print(f"\n❌ 清理过程中发生错误: {e}")
