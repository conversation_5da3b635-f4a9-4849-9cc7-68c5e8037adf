#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试修复后的LLM服务
"""

import os
import sys
import asyncio

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from src.services.llm_service import LLMService
from src.core.api_manager import APIManager
from src.utils.config_manager import ConfigManager

async def test_llm_service():
    """测试LLM服务的修复"""
    print("=" * 60)
    print("测试修复后的LLM服务")
    print("=" * 60)
    
    try:
        # 初始化配置管理器和API管理器
        print("🔧 初始化配置管理器...")
        config_manager = ConfigManager("config")

        print("🔧 初始化API管理器...")
        api_manager = APIManager(config_manager)
        await api_manager.initialize()
        
        # 初始化LLM服务
        print("🚀 初始化LLM服务...")
        llm_service = LLMService(api_manager)
        
        # 测试智谱AI
        print("\n🧪 测试智谱AI...")
        result = await llm_service.execute(
            provider="zhipu",
            prompt="你好，请简单回复一句话",
            max_tokens=50,
            temperature=0.7
        )
        
        if result.success:
            print(f"✅ 智谱AI成功: {result.data['content']}")
        else:
            print(f"❌ 智谱AI失败: {result.error}")
        
        # 测试Deepseek
        print("\n🧪 测试Deepseek...")
        result = await llm_service.execute(
            provider="deepseek",
            prompt="你好，请简单回复一句话",
            max_tokens=50,
            temperature=0.7
        )
        
        if result.success:
            print(f"✅ Deepseek成功: {result.data['content']}")
        else:
            print(f"❌ Deepseek失败: {result.error}")
        
        # 测试通义千问
        print("\n🧪 测试通义千问...")
        result = await llm_service.execute(
            provider="tongyi",
            prompt="你好，请简单回复一句话",
            max_tokens=50,
            temperature=0.7
        )
        
        if result.success:
            print(f"✅ 通义千问成功: {result.data['content']}")
        else:
            print(f"❌ 通义千问失败: {result.error}")
        
        # 测试故障转移机制
        print("\n🔄 测试故障转移机制...")
        result = await llm_service.execute_with_fallback(
            prompt="请用一句话介绍三国演义",
            max_tokens=100,
            temperature=0.7
        )
        
        if result.success:
            print(f"✅ 故障转移成功: {result.data['content']}")
            print(f"   使用的提供商: {result.metadata.get('provider', '未知')}")
        else:
            print(f"❌ 故障转移失败: {result.error}")
        
        print("\n" + "=" * 60)
        print("LLM服务测试完成")
        print("=" * 60)
        
    except Exception as e:
        print(f"❌ 测试过程中发生异常: {e}")
        import traceback
        print(f"详细错误: {traceback.format_exc()}")

if __name__ == "__main__":
    asyncio.run(test_llm_service())
