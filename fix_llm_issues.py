#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
LLM服务问题诊断和修复工具
"""

import os
import sys
import json
import requests

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def diagnose_llm_issues():
    """诊断LLM服务问题"""
    print("=" * 60)
    print("LLM服务问题诊断报告")
    print("=" * 60)
    
    # 读取配置
    try:
        with open('config/llm_config.json', 'r', encoding='utf-8') as f:
            config = json.load(f)
        models = config.get('models', [])
    except Exception as e:
        print(f"❌ 无法读取配置文件: {e}")
        return
    
    working_models = []
    failed_models = []
    
    for model in models:
        name = model.get('name', '未知')
        api_type = model.get('type', '')
        url = model.get('url', '')
        key = model.get('key', '')
        
        print(f"\n🔍 测试 {name} ({api_type}):")
        
        # 构造测试请求
        headers = {
            'Content-Type': 'application/json',
            'User-Agent': 'AI-Video-Generator/1.0'
        }
        
        if api_type == 'google':
            test_data = {
                "contents": [{"parts": [{"text": "Hello"}]}]
            }
            test_url = url + f"?key={key}"
        else:
            # 使用正确的模型名称
            model_name_map = {
                'zhipu': 'glm-4',
                'deepseek': 'deepseek-chat', 
                'tongyi': 'qwen-turbo'
            }
            model_name = model_name_map.get(api_type, 'gpt-3.5-turbo')
            
            test_data = {
                "model": model_name,
                "messages": [{"role": "user", "content": "Hello"}],
                "max_tokens": 10
            }
            test_url = url
            headers['Authorization'] = f"Bearer {key}"
        
        try:
            response = requests.post(
                test_url, 
                json=test_data, 
                headers=headers, 
                timeout=30,
                proxies={"http": None, "https": None}  # 禁用代理
            )
            
            if response.status_code == 200:
                print(f"✅ {name} 工作正常")
                working_models.append(name)
            elif response.status_code == 401:
                print(f"❌ {name} API密钥验证失败")
                failed_models.append((name, "API密钥无效"))
            elif response.status_code == 403:
                print(f"❌ {name} 访问被拒绝")
                failed_models.append((name, "访问权限问题"))
            elif response.status_code == 429:
                try:
                    error_data = response.json()
                    if "欠费" in str(error_data):
                        print(f"❌ {name} 账户欠费，需要充值")
                        failed_models.append((name, "账户欠费"))
                    else:
                        print(f"❌ {name} 请求频率限制")
                        failed_models.append((name, "请求频率限制"))
                except:
                    print(f"❌ {name} 请求频率限制")
                    failed_models.append((name, "请求频率限制"))
            elif response.status_code == 400:
                try:
                    error_data = response.json()
                    error_msg = str(error_data)
                    if "location" in error_msg.lower() and "not supported" in error_msg.lower():
                        print(f"❌ {name} 地区不支持")
                        failed_models.append((name, "地区不支持"))
                    else:
                        print(f"❌ {name} 请求格式错误: {error_msg[:100]}...")
                        failed_models.append((name, "请求格式错误"))
                except:
                    print(f"❌ {name} 请求格式错误")
                    failed_models.append((name, "请求格式错误"))
            else:
                print(f"❌ {name} 返回状态码: {response.status_code}")
                failed_models.append((name, f"状态码{response.status_code}"))
                
        except requests.exceptions.Timeout:
            print(f"❌ {name} 请求超时")
            failed_models.append((name, "请求超时"))
        except requests.exceptions.ConnectionError as e:
            print(f"❌ {name} 连接错误: {e}")
            failed_models.append((name, "连接错误"))
        except Exception as e:
            print(f"❌ {name} 测试失败: {e}")
            failed_models.append((name, "测试失败"))
    
    # 生成报告
    print("\n" + "=" * 60)
    print("诊断结果汇总")
    print("=" * 60)
    
    if working_models:
        print(f"\n✅ 可用的LLM服务 ({len(working_models)}个):")
        for model in working_models:
            print(f"   • {model}")
    
    if failed_models:
        print(f"\n❌ 不可用的LLM服务 ({len(failed_models)}个):")
        for model, reason in failed_models:
            print(f"   • {model}: {reason}")
    
    # 提供解决建议
    print("\n" + "=" * 60)
    print("解决建议")
    print("=" * 60)
    
    if not working_models:
        print("\n⚠️  所有LLM服务都不可用，建议:")
        print("1. 检查网络连接")
        print("2. 验证API密钥是否正确")
        print("3. 确认账户余额充足")
        print("4. 检查是否有代理或防火墙阻止连接")
    elif len(working_models) < len(models):
        print(f"\n⚠️  部分LLM服务不可用，但{working_models[0]}等服务正常")
        print("程序可以继续使用可用的服务")
        
        for model, reason in failed_models:
            if reason == "账户欠费":
                print(f"• {model}: 请登录官网充值")
            elif reason == "API密钥无效":
                print(f"• {model}: 请检查config/llm_config.json中的密钥")
            elif reason == "地区不支持":
                print(f"• {model}: 该服务在当前地区不可用")
    else:
        print("\n✅ 所有LLM服务都正常工作！")
    
    print(f"\n推荐使用顺序: {' > '.join(working_models)}")

if __name__ == "__main__":
    diagnose_llm_issues()
