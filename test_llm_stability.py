#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试LLM服务的稳定性改进
"""

import os
import sys
import asyncio
import time

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from src.services.llm_service import LLMService
from src.core.api_manager import APIManager
from src.utils.config_manager import ConfigManager

async def test_llm_stability():
    """测试LLM服务稳定性"""
    print("=" * 60)
    print("LLM服务稳定性测试（优化后）")
    print("=" * 60)
    
    try:
        # 初始化服务
        print("🔧 初始化服务...")
        config_manager = ConfigManager("config")
        api_manager = APIManager(config_manager)
        await api_manager.initialize()
        llm_service = LLMService(api_manager)
        
        print(f"📊 服务配置:")
        print(f"   最大重试次数: {llm_service.max_retries}")
        print(f"   重试延迟: {llm_service.retry_delay}秒")
        print(f"   退避因子: {llm_service.backoff_factor}")
        
        # 测试国内API的稳定性
        domestic_providers = ['zhipu', 'tongyi', 'deepseek']
        
        for provider in domestic_providers:
            print(f"\n🧪 测试 {provider} 稳定性...")
            
            success_count = 0
            total_time = 0
            
            # 连续测试5次
            for i in range(5):
                try:
                    start_time = time.time()
                    
                    result = await llm_service.execute(
                        provider=provider,
                        prompt=f"这是第{i+1}次测试，请简单回复",
                        max_tokens=30,
                        temperature=0.7
                    )
                    
                    end_time = time.time()
                    duration = end_time - start_time
                    total_time += duration
                    
                    if result.success:
                        success_count += 1
                        print(f"   ✅ 测试{i+1} 成功 ({duration:.2f}s): {result.data['content'][:30]}...")
                    else:
                        print(f"   ❌ 测试{i+1} 失败: {result.error}")
                        
                except Exception as e:
                    print(f"   ❌ 测试{i+1} 异常: {e}")
                
                # 短暂间隔
                await asyncio.sleep(0.5)
            
            avg_time = total_time / 5 if total_time > 0 else 0
            success_rate = (success_count / 5) * 100
            
            print(f"   📊 {provider} 结果: {success_count}/5 成功 ({success_rate:.1f}%), 平均耗时: {avg_time:.2f}s")
        
        # 测试故障转移的稳定性
        print(f"\n🔄 测试故障转移稳定性...")
        
        success_count = 0
        for i in range(3):
            try:
                start_time = time.time()
                
                result = await llm_service.execute_with_fallback(
                    prompt=f"故障转移测试{i+1}，请简单回复",
                    max_tokens=50,
                    temperature=0.7
                )
                
                end_time = time.time()
                duration = end_time - start_time
                
                if result.success:
                    success_count += 1
                    provider_used = result.metadata.get('provider', '未知')
                    print(f"   ✅ 故障转移{i+1} 成功 ({duration:.2f}s) - 使用: {provider_used}")
                else:
                    print(f"   ❌ 故障转移{i+1} 失败: {result.error}")
                    
            except Exception as e:
                print(f"   ❌ 故障转移{i+1} 异常: {e}")
            
            await asyncio.sleep(1)
        
        print(f"   📊 故障转移结果: {success_count}/3 成功")
        
        # 测试高负载情况
        print(f"\n🚀 测试高负载情况（10个并发请求）...")
        
        tasks = []
        for i in range(10):
            task = llm_service.execute_with_fallback(
                prompt=f"并发测试{i+1}，请回复数字{i+1}",
                max_tokens=20,
                temperature=0.7
            )
            tasks.append(task)
        
        start_time = time.time()
        results = await asyncio.gather(*tasks, return_exceptions=True)
        end_time = time.time()
        total_duration = end_time - start_time
        
        success_count = 0
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                print(f"   ❌ 并发{i+1} 异常: {result}")
            elif hasattr(result, 'success') and result.success:
                success_count += 1
                print(f"   ✅ 并发{i+1} 成功: {result.data['content'][:20]}...")
            else:
                print(f"   ❌ 并发{i+1} 失败: {getattr(result, 'error', '未知错误')}")
        
        print(f"   📊 高负载结果: {success_count}/10 成功，总耗时: {total_duration:.2f}s")
        
        print(f"\n📋 总结:")
        print(f"• 增加了重试次数到5次")
        print(f"• 优化了连接器设置")
        print(f"• 增加了超时时间到60秒")
        print(f"• 改进了连接池管理")
        
        if success_count >= 8:
            print(f"✅ 系统稳定性良好，可以正常使用")
        elif success_count >= 5:
            print(f"⚠️ 系统稳定性一般，建议检查网络")
        else:
            print(f"❌ 系统稳定性较差，需要进一步排查")
        
    except Exception as e:
        print(f"❌ 测试过程中发生异常: {e}")
        import traceback
        print(f"详细错误: {traceback.format_exc()}")

if __name__ == "__main__":
    asyncio.run(test_llm_stability())
