"""
项目管理器适配器 - 将新的ProjectManagerV2适配到现有系统
"""

import os
import json
from typing import Dict, List, Any, Optional
from datetime import datetime

from src.core.project_manager_v2 import ProjectManagerV2
from src.utils.logger import logger


class ProjectManagerAdapter:
    """项目管理器适配器，兼容现有接口"""
    
    def __init__(self, config_dir: str):
        self.config_dir = config_dir
        # 获取项目根目录
        project_root = os.path.dirname(os.path.dirname(os.path.dirname(__file__)))
        projects_dir = os.path.join(project_root, 'output')
        
        # 使用新的项目管理器
        self.pm_v2 = ProjectManagerV2(projects_dir)
        
        # 兼容性属性
        self.current_project = None
        self.current_project_name = None
        self.projects_dir = projects_dir
        
        logger.info(f"项目管理器适配器初始化，项目保存目录: {projects_dir}")
    
    def create_new_project(self, project_name: str, project_description: str = "") -> Dict[str, Any]:
        """创建新项目（兼容旧接口）"""
        try:
            # 使用新的项目管理器创建项目
            success = self.pm_v2.create_project(project_name, project_description)
            
            if success:
                # 获取项目数据并转换为旧格式
                project_data = self.pm_v2.get_project_data()
                
                # 转换为兼容格式
                compatible_data = self._convert_to_old_format(project_data)
                
                # 设置当前项目
                self.current_project = compatible_data
                self.current_project_name = project_name
                
                logger.info(f"项目创建成功: {project_name}")
                return compatible_data
            else:
                raise Exception("项目创建失败")
                
        except Exception as e:
            logger.error(f"创建项目失败: {e}")
            raise
    
    def load_project(self, project_path: str) -> Dict[str, Any]:
        """加载项目（兼容旧接口）"""
        try:
            # 使用新的项目管理器加载项目
            success = self.pm_v2.load_project(project_path)
            
            if success:
                # 获取项目数据并转换为旧格式
                project_data = self.pm_v2.get_project_data()
                
                # 转换为兼容格式
                compatible_data = self._convert_to_old_format(project_data)
                
                # 设置当前项目
                self.current_project = compatible_data
                self.current_project_name = compatible_data.get("project_name", "")
                
                logger.info(f"项目加载成功: {self.current_project_name}")
                return compatible_data
            else:
                raise Exception("项目加载失败")
                
        except Exception as e:
            logger.error(f"加载项目失败: {e}")
            raise
    
    def save_project(self) -> bool:
        """保存项目（兼容旧接口）"""
        try:
            if not self.current_project:
                logger.error("没有当前项目可保存")
                return False
            
            # 将当前项目数据同步到新的项目管理器
            self._sync_to_new_format()
            
            # 使用新的项目管理器保存
            return self.pm_v2.save_project()
            
        except Exception as e:
            logger.error(f"保存项目失败: {e}")
            return False
    
    def save_text_content(self, content: str, content_type: str) -> bool:
        """保存文本内容（兼容旧接口）"""
        try:
            if not self.current_project:
                logger.error("没有当前项目")
                return False
            
            # 更新当前项目数据
            if content_type == "original_text":
                self.current_project["original_text"] = content
                # 同步到新格式
                if self.pm_v2.current_project:
                    self.pm_v2.current_project["content"]["original_text"] = content
            elif content_type == "rewritten_text":
                self.current_project["rewritten_text"] = content
                # 同步到新格式
                if self.pm_v2.current_project:
                    self.pm_v2.current_project["content"]["rewritten_text"] = content
            
            # 保存项目
            return self.pm_v2.save_project()
            
        except Exception as e:
            logger.error(f"保存文本内容失败: {e}")
            return False
    
    def get_project_data(self) -> Optional[Dict]:
        """获取项目数据（兼容旧接口）"""
        return self.current_project
    
    def list_projects(self) -> List[Dict]:
        """列出所有项目（兼容旧接口）"""
        try:
            projects = []
            
            if os.path.exists(self.projects_dir):
                for item in os.listdir(self.projects_dir):
                    project_dir = os.path.join(self.projects_dir, item)
                    project_file = os.path.join(project_dir, "project.json")
                    
                    if os.path.isdir(project_dir) and os.path.exists(project_file):
                        try:
                            with open(project_file, 'r', encoding='utf-8') as f:
                                project_data = json.load(f)
                            
                            # 兼容新旧格式
                            project_info = project_data.get("project_info", {})
                            project_name = project_info.get("name") or project_data.get("project_name", item)
                            description = project_info.get("description") or project_data.get("description", "")
                            created_time = project_info.get("created_time") or project_data.get("created_time", "")
                            last_modified = project_info.get("last_modified") or project_data.get("last_modified", created_time)
                            
                            projects.append({
                                "name": project_name,
                                "description": description,
                                "created_time": created_time,
                                "last_modified": last_modified,
                                "path": project_dir
                            })
                            
                        except Exception as e:
                            logger.warning(f"读取项目文件失败: {project_file}, 错误: {e}")
                            continue
            
            return projects
            
        except Exception as e:
            logger.error(f"列出项目失败: {e}")
            return []
    
    def _convert_to_old_format(self, new_data: Dict) -> Dict:
        """将新格式转换为旧格式"""
        project_info = new_data.get("project_info", {})
        content = new_data.get("content", {})
        settings = new_data.get("settings", {})
        shots = new_data.get("shots", {})
        progress = new_data.get("progress", {})
        
        # 构建兼容的旧格式
        old_format = {
            "project_name": project_info.get("name", ""),
            "description": project_info.get("description", ""),
            "created_time": project_info.get("created_time", ""),
            "last_modified": project_info.get("last_modified", ""),
            "project_dir": project_info.get("project_dir", ""),
            "project_root": project_info.get("project_dir", ""),
            
            # 内容数据
            "original_text": content.get("original_text", ""),
            "rewritten_text": content.get("rewritten_text", ""),
            "five_stage_storyboard": content.get("five_stage_data", {}),
            
            # 设置数据
            "voice_generation": {
                "provider": settings.get("voice", {}).get("provider", "edge_tts"),
                "settings": {
                    "voice": settings.get("voice", {}).get("voice", "zh-CN-YunxiNeural"),
                    "speed": settings.get("voice", {}).get("speed", 1.0)
                },
                "voice_segments": self._convert_shots_to_voice_segments(shots)
            },
            
            "image_generation_settings": {
                "engine": settings.get("image", {}).get("engine", "pollinations"),
                "style": settings.get("image", {}).get("style", "realistic"),
                "quality": settings.get("image", {}).get("quality", "high"),
                "resolution": settings.get("image", {}).get("resolution", "1024x1024")
            },
            
            "video_generation": {
                "settings": {
                    "engine": settings.get("video", {}).get("engine", "cogvideox_flash"),
                    "duration": settings.get("video", {}).get("duration", 5.0),
                    "fps": settings.get("video", {}).get("fps", 30),
                    "width": settings.get("video", {}).get("width", 1024),
                    "height": settings.get("video", {}).get("height", 1024),
                    "motion_intensity": settings.get("video", {}).get("motion_intensity", 0.5),
                    "concurrent_tasks": settings.get("video", {}).get("concurrent_tasks", 3)
                },
                "videos": self._convert_shots_to_videos(shots),
                "progress": progress.get("video", {})
            },
            
            # 进度数据
            "progress_status": {
                "voice_generation": progress.get("voice", {}),
                "image_generation": progress.get("image", {}),
                "video_generation": progress.get("video", {})
            }
        }
        
        return old_format
    
    def _convert_shots_to_voice_segments(self, shots: Dict) -> List[Dict]:
        """将镜头数据转换为语音段格式"""
        segments = []
        for shot_id, shot in shots.items():
            voice_data = shot.get("voice", {})
            segments.append({
                "shot_id": shot_id,
                "scene_id": shot.get("scene_id", ""),
                "original_text": voice_data.get("text", ""),
                "audio_path": voice_data.get("audio_path"),
                "duration": voice_data.get("duration", 0),
                "status": voice_data.get("status", "pending")
            })
        return segments
    
    def _convert_shots_to_videos(self, shots: Dict) -> List[Dict]:
        """将镜头数据转换为视频格式"""
        videos = []
        for shot_id, shot in shots.items():
            video_data = shot.get("video", {})
            videos.append({
                "shot_id": shot_id,
                "video_path": video_data.get("path"),
                "segments": video_data.get("segments", []),
                "status": video_data.get("status", "pending")
            })
        return videos
    
    def _sync_to_new_format(self):
        """将当前项目数据同步到新格式"""
        if not self.current_project or not self.pm_v2.current_project:
            return
        
        # 同步基础信息
        self.pm_v2.current_project["project_info"]["last_modified"] = datetime.now().isoformat()
        
        # 同步内容
        content = self.pm_v2.current_project["content"]
        content["original_text"] = self.current_project.get("original_text", "")
        content["rewritten_text"] = self.current_project.get("rewritten_text", "")
        content["five_stage_data"] = self.current_project.get("five_stage_storyboard", {})
    
    def get_current_project_path(self) -> str:
        """获取当前项目根目录路径

        Returns:
            str: 当前项目根目录路径，如果没有当前项目则返回空字符串
        """
        if self.current_project:
            return self.current_project.get('project_dir', '')
        return ''

    def get_project_root(self) -> str:
        """获取当前项目根目录路径（兼容方法）

        Returns:
            str: 当前项目根目录路径
        """
        return self.get_current_project_path()

    def get_project_path(self, project_name: str) -> str:
        """获取项目根目录路径

        Args:
            project_name: 项目名称

        Returns:
            str: 项目根目录路径
        """
        if self.current_project and self.current_project.get('project_name') == project_name:
            return self.current_project.get('project_dir', '')

        # 如果不是当前项目，根据项目名称构建路径
        return os.path.join(self.projects_dir, project_name)

    def get_project_file_path(self, file_type: str, filename: str = None):
        """获取项目文件路径"""
        if not self.current_project:
            raise ValueError("没有当前项目")

        from pathlib import Path
        project_dir = Path(self.current_project["project_dir"])

        # 根据文件类型确定子目录
        type_mapping = {
            "original_text": "texts",
            "rewritten_text": "texts",
            "storyboard": "storyboard",
            "images": "images",
            "audio": "audio",
            "video": "video",
            "final_video": "video",
            "subtitles": "video",
            "exports": "exports"
        }

        if file_type not in type_mapping:
            raise ValueError(f"不支持的文件类型: {file_type}")

        subdir = project_dir / type_mapping[file_type]

        if filename:
            return subdir / filename
        else:
            return subdir

    def get_project_config_path(self, project_name: str) -> str:
        """获取项目配置文件路径"""
        return os.path.join(self.get_project_path(project_name), 'project.json')

    def get_character_scene_manager(self, service_manager):
        """获取角色场景管理器（兼容旧接口）"""
        try:
            from src.utils.character_scene_manager import CharacterSceneManager

            if self.current_project:
                project_dir = self.current_project.get("project_dir", "")
                if project_dir and os.path.exists(project_dir):
                    return CharacterSceneManager(project_dir, service_manager)
            
            return None
            
        except Exception as e:
            logger.error(f"获取角色场景管理器失败: {e}")
            return None
