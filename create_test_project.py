#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
创建测试项目用于UI测试
"""

import os
import sys

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from src.core.project_manager_adapter import ProjectManagerAdapter

def create_test_project():
    """创建测试项目"""
    print("创建测试项目用于UI测试...")
    
    # 初始化项目管理器
    config_dir = "config"
    pm = ProjectManagerAdapter(config_dir)
    
    # 创建测试项目
    test_project_name = "UI删除测试项目"
    print(f"创建测试项目: {test_project_name}")
    
    try:
        # 创建项目
        success = pm.create_new_project(test_project_name, "这是一个用于UI删除功能测试的项目")
        if success:
            print("✅ 测试项目创建成功")
            
            # 列出项目确认
            projects = pm.list_projects()
            for project in projects:
                if project["name"] == test_project_name:
                    print(f"✅ 项目路径: {project['path']}")
                    return True
        else:
            print("❌ 创建测试项目失败")
            return False
            
    except Exception as e:
        print(f"❌ 创建过程中发生错误: {e}")
        return False

if __name__ == "__main__":
    create_test_project()
