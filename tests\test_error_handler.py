#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
错误处理系统测试
"""

import unittest
import time
from unittest.mock import Mock, patch, MagicMock
from tests import BaseTestCase

from utils.error_handler import (
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ErrorClassifier, ErrorCategory, ErrorSeverity, ErrorInfo,
    NetworkChecker, handle_error, async_handle_exception, service_error_handler
)


class TestErrorClassifier(BaseTestCase):
    """错误分类器测试类"""
    
    def setUp(self):
        """测试设置"""
        super().setUp()
        self.classifier = ErrorClassifier()
    
    def test_network_error_classification(self):
        """测试网络错误分类"""
        network_errors = [
            ConnectionError("Connection failed"),
            TimeoutError("Request timeout"),
            Exception("network unreachable")
        ]
        
        for error in network_errors:
            category = self.classifier.classify_error(error)
            self.assertEqual(category, ErrorCategory.NETWORK)
    
    def test_api_error_classification(self):
        """测试API错误分类"""
        api_errors = [
            Exception("401 unauthorized"),
            Exception("api key invalid"),
            Exception("rate limit exceeded")
        ]
        
        for error in api_errors:
            category = self.classifier.classify_error(error)
            self.assertEqual(category, ErrorCategory.API)
    
    def test_file_error_classification(self):
        """测试文件错误分类"""
        file_errors = [
            FileNotFoundError("File not found"),
            PermissionError("Permission denied"),
            Exception("disk full")
        ]
        
        for error in file_errors:
            category = self.classifier.classify_error(error)
            self.assertEqual(category, ErrorCategory.FILE_IO)
    
    def test_validation_error_classification(self):
        """测试验证错误分类"""
        validation_errors = [
            ValueError("Invalid value"),
            TypeError("Type mismatch"),
            Exception("validation failed")
        ]
        
        for error in validation_errors:
            category = self.classifier.classify_error(error)
            self.assertEqual(category, ErrorCategory.VALIDATION)
    
    def test_system_error_classification(self):
        """测试系统错误分类"""
        system_errors = [
            MemoryError("Out of memory"),
            Exception("system error"),
            Exception("internal server error")
        ]
        
        for error in system_errors:
            category = self.classifier.classify_error(error)
            self.assertEqual(category, ErrorCategory.SYSTEM)
    
    def test_unknown_error_classification(self):
        """测试未知错误分类"""
        unknown_error = Exception("some random error")
        category = self.classifier.classify_error(unknown_error)
        self.assertEqual(category, ErrorCategory.UNKNOWN)
    
    def test_severity_determination(self):
        """测试错误严重性判断"""
        # 高严重性错误
        high_severity_errors = [
            (MemoryError(), ErrorCategory.SYSTEM),
            (Exception("critical"), ErrorCategory.SYSTEM)
        ]
        
        for error, category in high_severity_errors:
            severity = self.classifier.determine_severity(error, category)
            self.assertIn(severity, [ErrorSeverity.HIGH, ErrorSeverity.CRITICAL])
        
        # 中等严重性错误
        medium_severity_errors = [
            (ConnectionError(), ErrorCategory.NETWORK),
            (ValueError(), ErrorCategory.VALIDATION)
        ]
        
        for error, category in medium_severity_errors:
            severity = self.classifier.determine_severity(error, category)
            self.assertIn(severity, [ErrorSeverity.MEDIUM, ErrorSeverity.HIGH])
    
    def test_solution_generation(self):
        """测试解决方案生成"""
        solutions = self.classifier.get_solutions(ErrorCategory.NETWORK)
        self.assertIsInstance(solutions, list)
        self.assertTrue(len(solutions) > 0)
        
        # 检查解决方案内容
        for solution in solutions:
            self.assertIsInstance(solution, str)
            self.assertTrue(len(solution) > 0)


class TestNetworkChecker(BaseTestCase):
    """网络检查器测试类"""
    
    def setUp(self):
        """测试设置"""
        super().setUp()
        self.network_checker = NetworkChecker()
    
    @patch('socket.create_connection')
    @patch('requests.get')
    def test_network_check_success(self, mock_get, mock_socket):
        """测试网络检查成功"""
        # 模拟成功的网络连接
        mock_socket.return_value = MagicMock()
        mock_response = Mock()
        mock_response.status_code = 200
        mock_get.return_value = mock_response
        
        self.network_checker.check_connection()
        self.assertTrue(self.network_checker.get_connection_status())
    
    @patch('socket.create_connection')
    def test_network_check_failure(self, mock_socket):
        """测试网络检查失败"""
        # 模拟网络连接失败
        mock_socket.side_effect = Exception("Network unreachable")
        
        self.network_checker.check_connection()
        self.assertFalse(self.network_checker.get_connection_status())
    
    def test_force_check(self):
        """测试强制检查"""
        with patch.object(self.network_checker, 'check_connection') as mock_check:
            result = self.network_checker.force_check()
            mock_check.assert_called_once()
            self.assertIsInstance(result, bool)


class TestErrorHandler(BaseTestCase):
    """错误处理器测试类"""
    
    def setUp(self):
        """测试设置"""
        super().setUp()
        self.error_handler = ErrorHandler()
    
    def test_error_handling(self):
        """测试错误处理"""
        test_error = ValueError("Test error")
        context = {"operation": "test_operation"}
        
        error_info = self.error_handler.handle_exception(test_error, context)
        
        self.assertIsInstance(error_info, ErrorInfo)
        self.assertEqual(error_info.exception_type, "ValueError")
        self.assertEqual(error_info.message, "Test error")
        self.assertIsNotNone(error_info.category)
        self.assertIsNotNone(error_info.severity)
        self.assertIsInstance(error_info.solutions, list)
    
    def test_error_history(self):
        """测试错误历史记录"""
        initial_count = len(self.error_handler.error_history)
        
        # 处理一些错误
        errors = [
            ValueError("Error 1"),
            ConnectionError("Error 2"),
            FileNotFoundError("Error 3")
        ]
        
        for error in errors:
            self.error_handler.handle_exception(error)
        
        # 检查错误历史
        self.assertEqual(len(self.error_handler.error_history), initial_count + len(errors))
        
        # 检查最近的错误
        recent_errors = self.error_handler.get_recent_errors(2)
        self.assertEqual(len(recent_errors), 2)
    
    def test_error_statistics(self):
        """测试错误统计"""
        # 处理一些错误
        self.error_handler.handle_exception(ValueError("Test 1"))
        self.error_handler.handle_exception(ConnectionError("Test 2"))
        self.error_handler.handle_exception(ValueError("Test 3"))
        
        stats = self.error_handler.get_error_statistics()
        
        self.assertIsInstance(stats, dict)
        self.assertIn('total_errors', stats)
        self.assertIn('errors_by_category', stats)
        self.assertIn('errors_by_severity', stats)
        self.assertTrue(stats['total_errors'] >= 3)
    
    def test_error_clearing(self):
        """测试错误清理"""
        # 添加一些错误
        self.error_handler.handle_exception(ValueError("Test"))
        self.assertTrue(len(self.error_handler.error_history) > 0)
        
        # 清理错误
        self.error_handler.clear_error_history()
        self.assertEqual(len(self.error_handler.error_history), 0)
    
    def test_context_handling(self):
        """测试上下文处理"""
        context = {
            "user_id": "test_user",
            "operation": "test_operation",
            "timestamp": time.time()
        }
        
        error_info = self.error_handler.handle_exception(
            ValueError("Test error"), 
            context
        )
        
        self.assertIsNotNone(error_info.context)
        self.assertEqual(error_info.context["user_id"], "test_user")
        self.assertEqual(error_info.context["operation"], "test_operation")


class TestErrorDecorators(BaseTestCase):
    """错误处理装饰器测试类"""
    
    def test_async_handle_exception_decorator(self):
        """测试异步异常处理装饰器"""
        @async_handle_exception(show_to_user=False)
        async def test_async_function():
            raise ValueError("Test async error")
        
        import asyncio
        
        # 运行异步函数
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            result = loop.run_until_complete(test_async_function())
            # 装饰器应该捕获异常并返回None
            self.assertIsNone(result)
        finally:
            loop.close()
    
    def test_service_error_handler_decorator(self):
        """测试服务错误处理装饰器"""
        @service_error_handler(service_name="test_service", operation="test_operation")
        async def test_service_function():
            raise ConnectionError("Service connection failed")
        
        import asyncio
        
        # 运行服务函数
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            result = loop.run_until_complete(test_service_function())
            
            # 装饰器应该返回ServiceResult
            from core.service_base import ServiceResult
            self.assertIsInstance(result, ServiceResult)
            self.assertFalse(result.success)
            self.assertIsNotNone(result.error)
        finally:
            loop.close()


class TestConvenienceFunctions(BaseTestCase):
    """便捷函数测试类"""
    
    def test_handle_error_function(self):
        """测试handle_error便捷函数"""
        test_error = ValueError("Test error")
        
        error_info = handle_error(test_error, show_to_user=False)
        
        self.assertIsInstance(error_info, ErrorInfo)
        self.assertEqual(error_info.exception_type, "ValueError")
    
    @patch('utils.error_handler.error_handler')
    def test_check_network_function(self, mock_error_handler):
        """测试check_network便捷函数"""
        from utils.error_handler import check_network
        
        mock_network_checker = Mock()
        mock_network_checker.get_connection_status.return_value = True
        mock_error_handler.network_checker = mock_network_checker
        
        result = check_network()
        self.assertTrue(result)
    
    @patch('utils.error_handler.error_handler')
    def test_get_error_stats_function(self, mock_error_handler):
        """测试get_error_stats便捷函数"""
        from utils.error_handler import get_error_stats
        
        mock_stats = {"total_errors": 5}
        mock_error_handler.get_error_statistics.return_value = mock_stats
        
        stats = get_error_stats()
        self.assertEqual(stats, mock_stats)
    
    @patch('utils.error_handler.error_handler')
    def test_clear_errors_function(self, mock_error_handler):
        """测试clear_errors便捷函数"""
        from utils.error_handler import clear_errors
        
        clear_errors()
        mock_error_handler.clear_error_history.assert_called_once()


if __name__ == '__main__':
    unittest.main()
