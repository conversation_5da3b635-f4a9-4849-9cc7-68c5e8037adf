#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI视频生成器 - 自动安装脚本
支持Windows、Linux、macOS
"""

import os
import sys
import subprocess
import platform
from pathlib import Path

def print_banner():
    """打印安装横幅"""
    print("=" * 60)
    print("🎬 AI视频生成器 - 自动安装程序")
    print("=" * 60)
    print(f"Python版本: {sys.version}")
    print(f"操作系统: {platform.system()} {platform.release()}")
    print(f"架构: {platform.machine()}")
    print("=" * 60)

def check_python_version():
    """检查Python版本"""
    print("🔍 检查Python版本...")
    if sys.version_info < (3, 8):
        print("❌ 错误: 需要Python 3.8或更高版本")
        print(f"   当前版本: {sys.version}")
        print("   请升级Python后重试")
        return False
    print(f"✅ Python版本检查通过: {sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro}")
    return True

def check_pip():
    """检查pip是否可用"""
    print("🔍 检查pip...")
    try:
        import pip
        result = subprocess.run([sys.executable, "-m", "pip", "--version"], 
                              capture_output=True, text=True)
        if result.returncode == 0:
            print(f"✅ pip可用: {result.stdout.strip()}")
            return True
        else:
            print("❌ pip不可用")
            return False
    except ImportError:
        print("❌ pip未安装")
        return False

def create_virtual_environment():
    """创建虚拟环境"""
    print("🏗️ 创建虚拟环境...")
    venv_path = Path("venv")
    
    if venv_path.exists():
        print("⚠️ 虚拟环境已存在，跳过创建")
        return True
    
    try:
        result = subprocess.run([sys.executable, "-m", "venv", "venv"], 
                              capture_output=True, text=True)
        if result.returncode == 0:
            print("✅ 虚拟环境创建成功")
            return True
        else:
            print(f"❌ 虚拟环境创建失败: {result.stderr}")
            return False
    except Exception as e:
        print(f"❌ 虚拟环境创建异常: {e}")
        return False

def get_python_executable():
    """获取虚拟环境中的Python可执行文件路径"""
    if platform.system() == "Windows":
        return Path("venv/Scripts/python.exe")
    else:
        return Path("venv/bin/python")

def get_pip_executable():
    """获取虚拟环境中的pip可执行文件路径"""
    if platform.system() == "Windows":
        return Path("venv/Scripts/pip.exe")
    else:
        return Path("venv/bin/pip")

def install_dependencies():
    """安装依赖包"""
    print("📦 安装依赖包...")
    
    pip_exe = get_pip_executable()
    if not pip_exe.exists():
        print("❌ 虚拟环境中的pip不存在")
        return False
    
    # 升级pip
    print("⬆️ 升级pip...")
    result = subprocess.run([str(pip_exe), "install", "--upgrade", "pip"], 
                          capture_output=True, text=True)
    if result.returncode != 0:
        print(f"⚠️ pip升级失败: {result.stderr}")
    
    # 安装依赖
    requirements_files = ["requirements.txt", "requirements_minimal.txt"]
    
    for req_file in requirements_files:
        if Path(req_file).exists():
            print(f"📋 安装 {req_file}...")
            result = subprocess.run([str(pip_exe), "install", "-r", req_file], 
                                  capture_output=True, text=True)
            if result.returncode == 0:
                print(f"✅ {req_file} 安装成功")
                return True
            else:
                print(f"⚠️ {req_file} 安装失败，尝试下一个...")
                print(f"   错误信息: {result.stderr}")
    
    # 如果所有requirements文件都失败，尝试安装核心依赖
    print("🔧 尝试安装核心依赖...")
    core_packages = [
        "PyQt5>=5.15.0",
        "PyQtWebEngine>=5.15.0", 
        "aiohttp>=3.8.0",
        "requests>=2.28.0",
        "Pillow>=9.0.0",
        "numpy>=1.21.0",
        "openai>=1.0.0",
        "jieba>=0.42.0",
        "tqdm>=4.64.0",
        "colorama>=0.4.5",
        "pyyaml>=6.0"
    ]
    
    failed_packages = []
    for package in core_packages:
        print(f"   安装 {package}...")
        result = subprocess.run([str(pip_exe), "install", package], 
                              capture_output=True, text=True)
        if result.returncode != 0:
            failed_packages.append(package)
            print(f"   ❌ {package} 安装失败")
        else:
            print(f"   ✅ {package} 安装成功")
    
    if failed_packages:
        print(f"⚠️ 以下包安装失败: {failed_packages}")
        print("   程序可能无法完全正常运行")
    
    return len(failed_packages) < len(core_packages) // 2  # 如果超过一半失败则认为安装失败

def create_directories():
    """创建必要的目录"""
    print("📁 创建必要目录...")
    
    directories = [
        "output",
        "output/images", 
        "output/videos",
        "output/audio",
        "logs",
        "temp",
        "temp/cache",
        "temp/image_cache"
    ]
    
    for directory in directories:
        Path(directory).mkdir(parents=True, exist_ok=True)
    
    print("✅ 目录创建完成")
    return True

def create_config():
    """创建配置文件"""
    print("⚙️ 创建配置文件...")
    
    config_dir = Path("config")
    example_config = config_dir / "app_settings.example.json"
    target_config = config_dir / "app_settings.json"
    
    if example_config.exists() and not target_config.exists():
        import shutil
        shutil.copy(example_config, target_config)
        print("✅ 配置文件创建成功")
        print(f"   请编辑 {target_config} 添加你的API密钥")
    else:
        print("⚠️ 配置文件已存在或示例文件不存在")
    
    return True

def create_startup_script():
    """创建启动脚本"""
    print("🚀 创建启动脚本...")
    
    if platform.system() == "Windows":
        # 创建Windows批处理文件
        script_content = """@echo off
echo 启动AI视频生成器...
cd /d %~dp0
if exist venv\\Scripts\\activate.bat (
    call venv\\Scripts\\activate.bat
)
python main.py
pause
"""
        with open("start.bat", "w", encoding="utf-8") as f:
            f.write(script_content)
        print("✅ Windows启动脚本创建成功: start.bat")
    
    else:
        # 创建Unix shell脚本
        script_content = """#!/bin/bash
echo "启动AI视频生成器..."
cd "$(dirname "$0")"
if [ -f venv/bin/activate ]; then
    source venv/bin/activate
fi
python main.py
"""
        with open("start.sh", "w") as f:
            f.write(script_content)
        os.chmod("start.sh", 0o755)
        print("✅ Unix启动脚本创建成功: start.sh")
    
    return True

def test_installation():
    """测试安装"""
    print("🧪 测试安装...")
    
    python_exe = get_python_executable()
    if not python_exe.exists():
        print("❌ Python可执行文件不存在")
        return False
    
    # 测试核心模块导入
    test_script = """
import sys
try:
    import PyQt5
    print("✅ PyQt5导入成功")
except ImportError as e:
    print(f"❌ PyQt5导入失败: {e}")
    sys.exit(1)

try:
    import aiohttp
    print("✅ aiohttp导入成功")
except ImportError as e:
    print(f"❌ aiohttp导入失败: {e}")

try:
    import PIL
    print("✅ Pillow导入成功")
except ImportError as e:
    print(f"❌ Pillow导入失败: {e}")

print("🎉 核心模块测试完成")
"""
    
    result = subprocess.run([str(python_exe), "-c", test_script], 
                          capture_output=True, text=True)
    
    print(result.stdout)
    if result.stderr:
        print(result.stderr)
    
    return result.returncode == 0

def main():
    """主安装流程"""
    print_banner()
    
    # 检查Python版本
    if not check_python_version():
        return False
    
    # 检查pip
    if not check_pip():
        return False
    
    # 创建虚拟环境
    if not create_virtual_environment():
        return False
    
    # 安装依赖
    if not install_dependencies():
        print("⚠️ 依赖安装不完整，但继续安装...")
    
    # 创建目录
    if not create_directories():
        return False
    
    # 创建配置
    if not create_config():
        return False
    
    # 创建启动脚本
    if not create_startup_script():
        return False
    
    # 测试安装
    if not test_installation():
        print("⚠️ 安装测试失败，但安装可能仍然可用")
    
    print("\n" + "=" * 60)
    print("🎉 安装完成！")
    print("=" * 60)
    print("📋 下一步:")
    print("1. 编辑配置文件: config/app_settings.json")
    print("2. 添加你的API密钥")
    if platform.system() == "Windows":
        print("3. 双击 start.bat 启动程序")
    else:
        print("3. 运行 ./start.sh 启动程序")
    print("4. 或者运行: python main.py")
    print("\n📖 查看快速开始指南: QUICK_START_NEW.md")
    print("🆘 如有问题，请查看日志文件: logs/")
    print("=" * 60)
    
    return True

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n❌ 安装被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 安装过程中发生错误: {e}")
        sys.exit(1)
