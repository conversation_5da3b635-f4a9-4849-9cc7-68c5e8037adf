#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI视频生成器 - 简化安全检查脚本
检查项目中是否包含敏感信息
"""

import os
import re
from pathlib import Path

def check_sensitive_files():
    """检查敏感文件是否存在"""
    print("检查敏感配置文件...")
    
    sensitive_files = [
        'config/app_settings.json',
        'config/llm_config.json',
        'config/tts_config.json',
        'config/baidu_translate_config.py'
    ]
    
    found_sensitive = []
    
    for file_path in sensitive_files:
        if os.path.exists(file_path):
            found_sensitive.append(file_path)
            print(f"  发现敏感文件: {file_path}")
        else:
            print(f"  敏感文件已排除: {file_path}")
    
    return found_sensitive

def check_api_keys_in_files():
    """检查文件中的API密钥"""
    print("检查文件中的API密钥...")
    
    # 简单的API密钥模式
    api_key_patterns = [
        r'sk-[a-zA-Z0-9]{40,}',  # OpenAI API密钥
        r'AIza[0-9A-Za-z_-]{35}',  # Google API密钥
        r'AKIA[0-9A-Z]{16}',  # AWS访问密钥
    ]
    
    found_keys = []
    
    # 扫描主要文件
    scan_files = [
        'main.py',
        'simple_demo.py',
        'run_tests.py'
    ]
    
    for file_path in scan_files:
        if os.path.exists(file_path):
            try:
                with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                    content = f.read()
                
                for pattern in api_key_patterns:
                    matches = re.findall(pattern, content)
                    for match in matches:
                        # 排除明显的示例
                        if not any(placeholder in match.upper() for placeholder in ['YOUR_', 'EXAMPLE', 'TEST']):
                            found_keys.append((file_path, match))
                            
            except Exception as e:
                print(f"  无法检查文件 {file_path}: {e}")
    
    return found_keys

def main():
    """主检查流程"""
    print("=" * 50)
    print("AI视频生成器 - 安全检查")
    print("=" * 50)
    
    # 检查敏感文件
    sensitive_files = check_sensitive_files()
    print()
    
    # 检查API密钥
    api_keys = check_api_keys_in_files()
    print()
    
    # 生成报告
    print("=" * 50)
    print("安全检查报告")
    print("=" * 50)
    
    total_issues = len(sensitive_files) + len(api_keys)
    
    if total_issues == 0:
        print("安全检查通过！")
        print("未发现敏感信息")
        print("项目可以安全上传到GitHub")
        return True
    else:
        print(f"发现 {total_issues} 个安全问题:")
        
        if sensitive_files:
            print("敏感文件:")
            for file in sensitive_files:
                print(f"  - {file}")
        
        if api_keys:
            print("API密钥:")
            for file, key in api_keys:
                print(f"  - {file}: {key[:10]}...")
        
        print("请修复这些问题后再上传")
        return False

if __name__ == "__main__":
    try:
        success = main()
        exit(0 if success else 1)
    except Exception as e:
        print(f"安全检查过程中发生错误: {e}")
        exit(1)
