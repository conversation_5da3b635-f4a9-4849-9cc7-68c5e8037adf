#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试运行器

提供测试执行、覆盖率报告、性能分析等功能
"""

import unittest
import sys
import time
import json
import argparse
from pathlib import Path
from typing import Dict, List, Any, Optional
from dataclasses import dataclass
import importlib.util

# 尝试导入coverage，如果没有安装则提供fallback
try:
    import coverage
    COVERAGE_AVAILABLE = True
except ImportError:
    COVERAGE_AVAILABLE = False
    print("Warning: coverage.py not installed. Install with: pip install coverage")


@dataclass
class TestResult:
    """测试结果数据类"""
    test_name: str
    status: str  # 'PASS', 'FAIL', 'ERROR', 'SKIP'
    duration: float
    error_message: Optional[str] = None
    traceback: Optional[str] = None


@dataclass
class TestSuiteResult:
    """测试套件结果"""
    total_tests: int
    passed: int
    failed: int
    errors: int
    skipped: int
    duration: float
    coverage_percentage: Optional[float] = None
    test_results: List[TestResult] = None
    
    def __post_init__(self):
        if self.test_results is None:
            self.test_results = []
    
    @property
    def success_rate(self) -> float:
        """成功率"""
        if self.total_tests == 0:
            return 0.0
        return (self.passed / self.total_tests) * 100


class TestRunner:
    """增强的测试运行器"""
    
    def __init__(self, test_dir: str = "tests", coverage_enabled: bool = True):
        """
        初始化测试运行器
        
        Args:
            test_dir: 测试目录
            coverage_enabled: 是否启用覆盖率分析
        """
        self.test_dir = Path(test_dir)
        self.coverage_enabled = coverage_enabled and COVERAGE_AVAILABLE
        self.coverage = None
        
        if self.coverage_enabled:
            self.coverage = coverage.Coverage(
                source=['src'],
                omit=[
                    '*/tests/*',
                    '*/test_*',
                    '*/__pycache__/*',
                    '*/venv/*',
                    '*/env/*'
                ]
            )
    
    def discover_tests(self) -> List[str]:
        """发现测试文件"""
        test_files = []
        
        if not self.test_dir.exists():
            print(f"测试目录不存在: {self.test_dir}")
            return test_files
        
        # 查找所有test_*.py文件
        for test_file in self.test_dir.glob("test_*.py"):
            if test_file.is_file():
                test_files.append(str(test_file))
        
        return test_files
    
    def load_test_suite(self, test_files: Optional[List[str]] = None) -> unittest.TestSuite:
        """加载测试套件"""
        if test_files is None:
            test_files = self.discover_tests()
        
        suite = unittest.TestSuite()
        loader = unittest.TestLoader()
        
        for test_file in test_files:
            try:
                # 动态导入测试模块
                module_name = Path(test_file).stem
                spec = importlib.util.spec_from_file_location(module_name, test_file)
                module = importlib.util.module_from_spec(spec)
                spec.loader.exec_module(module)
                
                # 加载测试
                module_suite = loader.loadTestsFromModule(module)
                suite.addTest(module_suite)
                
            except Exception as e:
                print(f"加载测试文件失败 {test_file}: {e}")
        
        return suite
    
    def run_tests(self, test_files: Optional[List[str]] = None, 
                  verbosity: int = 2) -> TestSuiteResult:
        """
        运行测试
        
        Args:
            test_files: 要运行的测试文件列表
            verbosity: 详细程度
            
        Returns:
            TestSuiteResult: 测试结果
        """
        start_time = time.time()
        
        # 启动覆盖率分析
        if self.coverage_enabled:
            self.coverage.start()
        
        try:
            # 加载测试套件
            suite = self.load_test_suite(test_files)
            
            # 创建自定义测试结果收集器
            result_collector = DetailedTestResult()
            
            # 运行测试
            runner = unittest.TextTestRunner(
                stream=sys.stdout,
                verbosity=verbosity,
                resultclass=lambda stream, descriptions, verbosity: result_collector
            )
            
            test_result = runner.run(suite)
            
            # 停止覆盖率分析
            coverage_percentage = None
            if self.coverage_enabled:
                self.coverage.stop()
                coverage_percentage = self._calculate_coverage()
            
            # 计算总时间
            duration = time.time() - start_time
            
            # 创建结果对象
            suite_result = TestSuiteResult(
                total_tests=test_result.testsRun,
                passed=test_result.testsRun - len(test_result.failures) - len(test_result.errors),
                failed=len(test_result.failures),
                errors=len(test_result.errors),
                skipped=len(getattr(test_result, 'skipped', [])),
                duration=duration,
                coverage_percentage=coverage_percentage,
                test_results=result_collector.detailed_results
            )
            
            return suite_result
            
        except Exception as e:
            print(f"运行测试时发生错误: {e}")
            return TestSuiteResult(
                total_tests=0,
                passed=0,
                failed=0,
                errors=1,
                skipped=0,
                duration=time.time() - start_time
            )
    
    def _calculate_coverage(self) -> Optional[float]:
        """计算覆盖率"""
        if not self.coverage_enabled:
            return None
        
        try:
            return self.coverage.report(show_missing=False)
        except Exception as e:
            print(f"计算覆盖率失败: {e}")
            return None
    
    def generate_coverage_report(self, output_dir: str = "coverage_report"):
        """生成覆盖率报告"""
        if not self.coverage_enabled:
            print("覆盖率分析未启用")
            return
        
        try:
            output_path = Path(output_dir)
            output_path.mkdir(exist_ok=True)
            
            # 生成HTML报告
            self.coverage.html_report(directory=str(output_path))
            print(f"覆盖率HTML报告已生成: {output_path}/index.html")
            
            # 生成XML报告
            xml_file = output_path / "coverage.xml"
            self.coverage.xml_report(outfile=str(xml_file))
            print(f"覆盖率XML报告已生成: {xml_file}")
            
        except Exception as e:
            print(f"生成覆盖率报告失败: {e}")
    
    def generate_json_report(self, result: TestSuiteResult, 
                           output_file: str = "test_report.json"):
        """生成JSON格式的测试报告"""
        try:
            report_data = {
                "summary": {
                    "total_tests": result.total_tests,
                    "passed": result.passed,
                    "failed": result.failed,
                    "errors": result.errors,
                    "skipped": result.skipped,
                    "success_rate": result.success_rate,
                    "duration": result.duration,
                    "coverage_percentage": result.coverage_percentage
                },
                "test_results": [
                    {
                        "test_name": tr.test_name,
                        "status": tr.status,
                        "duration": tr.duration,
                        "error_message": tr.error_message,
                        "traceback": tr.traceback
                    }
                    for tr in result.test_results
                ],
                "timestamp": time.time()
            }
            
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(report_data, f, indent=2, ensure_ascii=False)
            
            print(f"JSON测试报告已生成: {output_file}")
            
        except Exception as e:
            print(f"生成JSON报告失败: {e}")


class DetailedTestResult(unittest.TestResult):
    """详细的测试结果收集器"""
    
    def __init__(self):
        super().__init__()
        self.detailed_results: List[TestResult] = []
        self.test_start_times: Dict[str, float] = {}
    
    def startTest(self, test):
        """测试开始"""
        super().startTest(test)
        test_name = self._get_test_name(test)
        self.test_start_times[test_name] = time.time()
    
    def stopTest(self, test):
        """测试结束"""
        super().stopTest(test)
        test_name = self._get_test_name(test)
        duration = time.time() - self.test_start_times.get(test_name, 0)
        
        # 默认为通过，如果有错误会在其他方法中更新
        if not any(tr.test_name == test_name for tr in self.detailed_results):
            self.detailed_results.append(TestResult(
                test_name=test_name,
                status="PASS",
                duration=duration
            ))
    
    def addError(self, test, err):
        """添加错误"""
        super().addError(test, err)
        test_name = self._get_test_name(test)
        duration = time.time() - self.test_start_times.get(test_name, 0)
        
        self._update_or_add_result(test_name, "ERROR", duration, str(err[1]), 
                                 self._format_traceback(err))
    
    def addFailure(self, test, err):
        """添加失败"""
        super().addFailure(test, err)
        test_name = self._get_test_name(test)
        duration = time.time() - self.test_start_times.get(test_name, 0)
        
        self._update_or_add_result(test_name, "FAIL", duration, str(err[1]), 
                                 self._format_traceback(err))
    
    def addSkip(self, test, reason):
        """添加跳过"""
        super().addSkip(test, reason)
        test_name = self._get_test_name(test)
        duration = time.time() - self.test_start_times.get(test_name, 0)
        
        self._update_or_add_result(test_name, "SKIP", duration, reason)
    
    def _get_test_name(self, test) -> str:
        """获取测试名称"""
        return f"{test.__class__.__module__}.{test.__class__.__name__}.{test._testMethodName}"
    
    def _update_or_add_result(self, test_name: str, status: str, duration: float,
                            error_message: Optional[str] = None,
                            traceback: Optional[str] = None):
        """更新或添加测试结果"""
        # 查找现有结果
        for result in self.detailed_results:
            if result.test_name == test_name:
                result.status = status
                result.duration = duration
                result.error_message = error_message
                result.traceback = traceback
                return
        
        # 添加新结果
        self.detailed_results.append(TestResult(
            test_name=test_name,
            status=status,
            duration=duration,
            error_message=error_message,
            traceback=traceback
        ))
    
    def _format_traceback(self, err) -> str:
        """格式化traceback"""
        import traceback
        return ''.join(traceback.format_exception(*err))


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="AI视频生成器测试运行器")
    parser.add_argument("--test-dir", default="tests", help="测试目录")
    parser.add_argument("--no-coverage", action="store_true", help="禁用覆盖率分析")
    parser.add_argument("--verbosity", type=int, default=2, help="详细程度 (0-2)")
    parser.add_argument("--output-dir", default="test_output", help="输出目录")
    parser.add_argument("--json-report", help="JSON报告文件名")
    
    args = parser.parse_args()
    
    # 创建测试运行器
    runner = TestRunner(
        test_dir=args.test_dir,
        coverage_enabled=not args.no_coverage
    )
    
    print("开始运行测试...")
    print("=" * 60)
    
    # 运行测试
    result = runner.run_tests(verbosity=args.verbosity)
    
    # 打印结果摘要
    print("\n" + "=" * 60)
    print("测试结果摘要:")
    print(f"总测试数: {result.total_tests}")
    print(f"通过: {result.passed}")
    print(f"失败: {result.failed}")
    print(f"错误: {result.errors}")
    print(f"跳过: {result.skipped}")
    print(f"成功率: {result.success_rate:.1f}%")
    print(f"执行时间: {result.duration:.2f}秒")
    
    if result.coverage_percentage is not None:
        print(f"代码覆盖率: {result.coverage_percentage:.1f}%")
    
    # 生成报告
    output_dir = Path(args.output_dir)
    output_dir.mkdir(exist_ok=True)
    
    if runner.coverage_enabled:
        runner.generate_coverage_report(str(output_dir / "coverage"))
    
    if args.json_report:
        runner.generate_json_report(result, str(output_dir / args.json_report))
    
    # 返回适当的退出码
    sys.exit(0 if result.failed == 0 and result.errors == 0 else 1)


if __name__ == "__main__":
    main()
