# -*- coding: utf-8 -*-
"""
CogView-3-Flash 图像生成引擎实现
智谱AI的免费图像生成模型，支持文生图功能
"""

import asyncio
import aiohttp
import os
import time
import json
import base64
from typing import List, Dict, Optional, Callable
from ..image_engine_base import (
    ImageGenerationEngine, EngineType, EngineStatus, 
    GenerationConfig, GenerationResult, EngineInfo, ConfigConverter
)
from src.utils.logger import logger


class CogView3FlashEngine(ImageGenerationEngine):
    """CogView-3-Flash 引擎实现"""
    
    def __init__(self, config: Dict = None):
        super().__init__(EngineType.COGVIEW_3_FLASH)
        self.config = config or {}
        
        # API配置
        self.api_key = self.config.get('api_key', '')
        self.base_url = self.config.get('base_url', 'https://open.bigmodel.cn/api/paas/v4')
        self.model = self.config.get('model', 'cogview-3-flash')
        
        # 请求配置
        self.timeout = self.config.get('timeout', 300)  # 5分钟超时
        self.max_retries = self.config.get('max_retries', 3)
        self.concurrent_limit = self.config.get('concurrent_limit', 3)  # 并发限制
        
        # 输出配置
        self.output_dir = self.config.get('output_dir', 'output/images')
        
        # HTTP会话
        self.session: Optional[aiohttp.ClientSession] = None
        
        # 项目相关信息
        self.project_manager = None
        self.current_project_name = None
        
        if not self.api_key:
            logger.warning("CogView-3-Flash引擎未配置API密钥")
    
    async def initialize(self) -> bool:
        """初始化引擎"""
        try:
            if not self.api_key:
                self.status = EngineStatus.ERROR
                self.last_error = "缺少智谱AI API密钥"
                return False

            # 获取输出目录，不在初始化时创建
            self.output_dir = self._get_output_dir()

            # 延迟创建HTTP会话，避免初始化时的异步问题
            self.session = None

            # 测试连接
            if await self.test_connection():
                self.status = EngineStatus.IDLE
                logger.info("CogView-3-Flash引擎初始化成功")
                return True
            else:
                self.status = EngineStatus.ERROR
                logger.error("CogView-3-Flash引擎连接测试失败")
                return False

        except Exception as e:
            self.status = EngineStatus.ERROR
            self.last_error = str(e)
            logger.error(f"CogView-3-Flash引擎初始化失败: {e}")
            return False
    
    async def _ensure_session(self):
        """确保HTTP会话已创建"""
        if not self.session:
            headers = {
                'Authorization': f'Bearer {self.api_key}',
                'Content-Type': 'application/json'
            }
            timeout = aiohttp.ClientTimeout(total=300)
            self.session = aiohttp.ClientSession(headers=headers, timeout=timeout)

    async def test_connection(self) -> bool:
        """测试连接"""
        try:
            # 简化连接测试，只检查API密钥和基础URL是否配置
            if not self.api_key or not self.base_url:
                logger.error("CogView-3-Flash API密钥或基础URL未配置")
                return False

            # 不进行实际的API调用，避免初始化时的网络问题
            logger.info("CogView-3-Flash引擎配置检查通过")
            return True

        except Exception as e:
            logger.error(f"CogView-3-Flash连接测试失败: {e}")
            return False

    async def cleanup(self):
        """清理资源"""
        if self.session:
            await self.session.close()
            self.session = None
            logger.debug("CogView-3-Flash引擎会话已关闭")

    def __del__(self):
        """析构函数，确保资源清理"""
        if self.session and not self.session.closed:
            import asyncio
            try:
                loop = asyncio.get_event_loop()
                if loop.is_running():
                    loop.create_task(self.cleanup())
                else:
                    loop.run_until_complete(self.cleanup())
            except Exception:
                pass  # 忽略清理时的错误

    async def generate(self, config: GenerationConfig,
                      progress_callback: Optional[Callable] = None,
                      project_manager=None, current_project_name=None) -> GenerationResult:
        """生成图像"""
        start_time = time.time()
        self.status = EngineStatus.BUSY

        # 设置项目信息
        if project_manager and current_project_name:
            self.project_manager = project_manager
            self.current_project_name = current_project_name
            # 更新输出目录
            self.output_dir = self._get_output_dir(project_manager, current_project_name)

        try:
            if progress_callback:
                progress_callback("准备CogView-3-Flash生成请求...")

            # 转换配置
            cogview_config = self._convert_config(config)

            # 验证配置
            if not self._validate_config(cogview_config):
                raise Exception("配置验证失败")

            if progress_callback:
                progress_callback("发送请求到智谱AI...")

            # 发送生成请求
            image_urls = await self._generate_images(cogview_config)

            if progress_callback:
                progress_callback("下载生成的图像...")

            # 下载图像到本地
            image_paths = await self._download_images(image_urls)

            generation_time = time.time() - start_time
            success = len(image_paths) > 0

            # 计算成本（CogView-3-Flash是免费的）
            cost = 0.0

            # 更新统计
            error_msg = "" if success else "生成失败"
            self.update_stats(success, cost, error_msg)

            result = GenerationResult(
                success=success,
                image_paths=image_paths,
                generation_time=generation_time,
                cost=cost,
                engine_type=self.engine_type,
                metadata={
                    'model': self.model,
                    'cogview_config': cogview_config,
                    'image_urls': image_urls
                }
            )

            if not success:
                result.error_message = "未能生成任何图像"

            return result

        except Exception as e:
            error_msg = f"CogView-3-Flash生成失败: {e}"
            logger.error(error_msg)
            self.update_stats(False, 0.0, error_msg)

            return GenerationResult(
                success=False,
                error_message=error_msg,
                engine_type=self.engine_type
            )

    def _convert_config(self, config: GenerationConfig) -> Dict:
        """转换配置为CogView-3-Flash格式"""
        # 支持的尺寸
        supported_sizes = [
            "1024x1024", "1280x720", "720x1280", "1440x720", "720x1440",
            "1024x768", "768x1024", "1152x896", "896x1152", "1216x832", "832x1216"
        ]

        # 确定尺寸
        size = f"{config.width}x{config.height}"
        if size not in supported_sizes:
            # 使用最接近的支持尺寸
            size = "1024x1024"  # 默认尺寸
            logger.warning(f"不支持的尺寸 {config.width}x{config.height}，使用默认尺寸 {size}")

        cogview_config = {
            "model": self.model,
            "prompt": config.prompt,
            "size": size,
            "n": min(config.batch_size, 4),  # CogView-3-Flash最多支持4张
            "response_format": "url"
        }

        # 添加可选参数
        if config.seed is not None and config.seed > 0:
            cogview_config["seed"] = config.seed

        return cogview_config

    def _validate_config(self, config: Dict) -> bool:
        """验证配置"""
        # 检查提示词长度
        if len(config['prompt']) > 1000:
            logger.error("CogView-3-Flash提示词长度超过1000字符")
            return False

        # 检查批次大小
        if config['n'] > 4:
            logger.error("CogView-3-Flash批次大小不能超过4")
            return False

        return True

    async def _generate_images(self, config: Dict) -> List[str]:
        """生成图像并返回URL列表"""
        try:
            # 确保会话已创建
            await self._ensure_session()

            logger.debug(f"CogView-3-Flash请求数据: {config}")

            # 发送请求
            async with self.session.post(
                f"{self.base_url}/images/generations",
                json=config
            ) as response:

                if response.status == 200:
                    result = await response.json()

                    # 检查响应格式
                    if 'data' in result:
                        image_urls = [item['url'] for item in result['data']]
                        logger.info(f"CogView-3-Flash生成成功，获得 {len(image_urls)} 个图像URL")
                        return image_urls
                    else:
                        logger.error(f"CogView-3-Flash响应格式错误: {result}")
                        raise Exception("API响应格式错误")
                else:
                    error_text = await response.text()
                    logger.error(f"CogView-3-Flash API错误: {response.status} - {error_text}")
                    raise Exception(f"API请求失败: {response.status}")

        except Exception as e:
            logger.error(f"CogView-3-Flash图像生成失败: {e}")
            raise

    async def _download_images(self, image_urls: List[str]) -> List[str]:
        """下载图像到本地"""
        # 确保会话已创建
        await self._ensure_session()

        downloaded_paths = []

        for i, url in enumerate(image_urls):
            try:
                # 生成本地文件名
                timestamp = int(time.time() * 1000)
                filename = f"cogview3_flash_{timestamp}_{i}.png"
                # 使用当前的输出目录（可能已更新为项目目录）
                current_output_dir = self._get_output_dir(self.project_manager, self.current_project_name)
                filepath = os.path.join(current_output_dir, filename)

                # 下载图像
                async with self.session.get(url) as response:
                    if response.status == 200:
                        with open(filepath, 'wb') as f:
                            async for chunk in response.content.iter_chunked(8192):
                                f.write(chunk)

                        downloaded_paths.append(filepath)
                        logger.info(f"图像已下载: {filepath}")
                    else:
                        logger.error(f"下载图像失败: HTTP {response.status}")

            except Exception as e:
                logger.error(f"下载图像 {i} 失败: {e}")

        return downloaded_paths

    def _get_output_dir(self, project_manager=None, current_project_name=None) -> str:
        """获取输出目录"""
        try:
            # 优先使用传入的项目管理器
            if project_manager and current_project_name:
                project_root = project_manager.get_project_root(current_project_name)
                if project_root:
                    output_dir = os.path.join(project_root, 'images', 'cogview3_flash')
                    os.makedirs(output_dir, exist_ok=True)
                    return output_dir

            # 尝试使用实例变量
            if self.project_manager and self.current_project_name:
                project_root = self.project_manager.get_project_root(self.current_project_name)
                if project_root:
                    output_dir = os.path.join(project_root, 'images', 'cogview3_flash')
                    os.makedirs(output_dir, exist_ok=True)
                    return output_dir

            # 尝试获取全局项目管理器
            from src.core.project_manager import ProjectManager
            project_manager = ProjectManager()
            current_project = project_manager.get_current_project()

            if current_project:
                project_root = current_project.get('project_root')
                if project_root:
                    output_dir = os.path.join(project_root, 'images', 'cogview3_flash')
                    os.makedirs(output_dir, exist_ok=True)
                    return output_dir
        except Exception as e:
            logger.warning(f"无法获取项目目录: {e}")

        # 默认输出目录
        default_dir = os.path.join(os.getcwd(), 'output', 'images', 'cogview3_flash')
        os.makedirs(default_dir, exist_ok=True)
        return default_dir

    def get_available_models(self) -> List[str]:
        """获取可用模型"""
        return ['cogview-3-flash']

    def get_engine_info(self) -> EngineInfo:
        """获取引擎信息"""
        return EngineInfo(
            name="CogView-3-Flash",
            version="1.0",
            description="智谱AI的免费图像生成模型，支持多种分辨率的高质量图像生成，支持3并发任务",
            is_free=True,
            supports_batch=True,
            supports_custom_models=False,
            max_batch_size=4,
            supported_sizes=[
                (1024, 1024), (1280, 720), (720, 1280), (1440, 720), (720, 1440),
                (1024, 768), (768, 1024), (1152, 896), (896, 1152), (1216, 832), (832, 1216)
            ],
            cost_per_image=0.0,  # 免费
            rate_limit=300  # 支持3并发，每分钟300次
        )

    async def cleanup(self):
        """清理资源"""
        if self.session:
            await self.session.close()
            self.session = None

        self.status = EngineStatus.OFFLINE
        await super().cleanup()
