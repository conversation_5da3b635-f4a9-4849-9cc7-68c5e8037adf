#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试智谱AI支持的模型名称
"""

import requests
import json

def test_zhipu_models():
    """测试智谱AI不同的模型名称"""
    api_key = "ed7ffe9976bb4484ab16647564c0cb27.rI1BXVjDDDURMtJY"
    api_url = "https://open.bigmodel.cn/api/paas/v4/chat/completions"
    
    # 智谱AI可能支持的模型名称
    models_to_test = [
        "glm-4",
        "glm-4-flash", 
        "glm-4-plus",
        "glm-4-air",
        "glm-4-airx",
        "glm-4-long",
        "glm-3-turbo"
    ]
    
    headers = {
        'Authorization': f'Bearer {api_key}',
        'Content-Type': 'application/json'
    }
    
    print("=" * 60)
    print("智谱AI模型名称测试")
    print("=" * 60)
    
    working_models = []
    
    for model_name in models_to_test:
        print(f"\n🔍 测试模型: {model_name}")
        
        test_data = {
            "model": model_name,
            "messages": [{"role": "user", "content": "你好"}],
            "max_tokens": 10
        }
        
        try:
            response = requests.post(
                api_url,
                json=test_data,
                headers=headers,
                timeout=30,
                proxies={"http": None, "https": None}
            )
            
            if response.status_code == 200:
                result = response.json()
                content = result['choices'][0]['message']['content']
                print(f"✅ {model_name} 工作正常: {content}")
                working_models.append(model_name)
            else:
                error_data = response.json()
                error_msg = error_data.get('error', {}).get('message', '未知错误')
                print(f"❌ {model_name} 失败: {error_msg}")
                
        except Exception as e:
            print(f"❌ {model_name} 异常: {e}")
    
    print("\n" + "=" * 60)
    print("测试结果汇总")
    print("=" * 60)
    
    if working_models:
        print(f"✅ 可用的模型 ({len(working_models)}个):")
        for model in working_models:
            print(f"   • {model}")
        print(f"\n推荐使用: {working_models[0]}")
    else:
        print("❌ 没有找到可用的模型")

if __name__ == "__main__":
    test_zhipu_models()
