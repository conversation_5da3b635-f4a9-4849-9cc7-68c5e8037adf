# AI视频生成器 🎬

[![Python](https://img.shields.io/badge/Python-3.8+-blue.svg)](https://python.org)
[![PyQt5](https://img.shields.io/badge/PyQt5-5.15+-green.svg)](https://pypi.org/project/PyQt5/)
[![License](https://img.shields.io/badge/License-MIT-yellow.svg)](LICENSE)

一个功能强大的AI驱动视频生成工具，采用现代化模块架构，支持从文本到视频的完整工作流程。通过集成多种AI服务提供商，实现智能分镜、图像生成、语音合成和视频制作的全自动化流程。

## ✨ 核心特性

### 🏗️ 现代化架构
- **模块化设计**: 核心层、服务层、处理器层、界面层清晰分离
- **异步处理**: 全面支持异步操作，非阻塞用户界面
- **服务管理**: 统一的API管理和服务生命周期管理
- **错误处理**: 完善的错误处理和重试机制

### 🤖 多AI服务支持
- **LLM服务**: DeepSeek、通义千问、智谱AI、Google Gemini、OpenAI、SiliconFlow
- **图像生成**: Pollinations、ComfyUI、DALL-E、Stability AI、Google Imagen
- **语音服务**: Azure TTS、ElevenLabs、OpenAI TTS、Edge TTS
- **语音识别**: Azure STT、OpenAI Whisper

### 🎨 智能内容生成
- **五阶段分镜系统**: 世界观构建 → 场景分析 → 分镜生成 → 优化润色 → 最终输出
- **角色场景一致性**: 智能角色检测和场景连贯性保持
- **风格化处理**: 多种预设风格和自定义风格支持
- **批量处理**: 支持大规模内容批量生成

### 🔧 高级功能
- **工作流编排**: 可视化工作流设计和执行
- **项目管理**: 完整的项目保存、加载和版本管理
- **实时预览**: 生成过程实时预览和进度跟踪
- **多格式输出**: 支持多种视频、音频、图像格式

## 🚀 快速开始

### 环境要求
- Python 3.8+
- Windows 10/11 (主要支持平台)
- 4GB+ RAM
- 稳定的网络连接

### 自动安装 (推荐)
```bash
# 克隆项目
git clone https://github.com/your-repo/AI_Video_Generator.git
cd AI_Video_Generator

# 运行自动安装脚本
python setup_environment.py

# 启动程序
start.bat
```

### 手动安装
```bash
# 1. 创建虚拟环境
python -m venv venv
venv\Scripts\activate  # Windows
# source venv/bin/activate  # Linux/Mac

# 2. 安装依赖
pip install -r requirements.txt

# 3. 启动程序
python main.py
```

## 📁 项目结构

```
AI_Video_Generator/
├── src/                          # 源代码目录
│   ├── core/                     # 核心架构层
│   │   ├── api_manager.py        # API管理器
│   │   ├── service_base.py       # 服务基类
│   │   ├── service_manager.py    # 服务管理器
│   │   └── app_controller.py     # 应用控制器
│   ├── services/                 # AI服务层
│   │   ├── llm_service.py        # 大语言模型服务
│   │   ├── image_service.py      # 图像生成服务
│   │   └── voice_service.py      # 语音服务
│   ├── processors/               # 业务处理层
│   │   ├── text_processor.py     # 文本处理器
│   │   ├── image_processor.py    # 图像处理器
│   │   └── video_processor.py    # 视频处理器
│   ├── gui/                      # 用户界面层
│   │   ├── new_main_window.py    # 主窗口
│   │   ├── five_stage_storyboard_tab.py  # 五阶段分镜
│   │   ├── ai_drawing_tab.py     # AI绘图界面
│   │   ├── consistency_control_panel.py # 一致性控制
│   │   └── ...                   # 其他界面组件
│   ├── models/                   # 数据模型和引擎
│   │   ├── engines/              # 图像生成引擎
│   │   ├── image_generation_service.py
│   │   └── ...
│   └── utils/                    # 工具模块
├── config/                       # 配置文件
│   ├── app_settings.example.json # 应用配置示例
│   ├── llm_config.json          # LLM配置
│   ├── image_generation_config.py # 图像生成配置
│   └── workflows/               # ComfyUI工作流
├── output/                      # 输出目录
├── logs/                        # 日志文件
├── tests/                       # 测试文件
├── main.py                      # 主程序入口
├── start.bat                    # Windows启动脚本
└── requirements.txt             # 依赖列表
```

## 🎯 主要功能模块

### 📝 文本处理与分镜生成
- **智能文本分析**: 自动提取关键信息和情感色彩
- **五阶段分镜流程**: 
  1. 世界观构建 - 分析文本背景和设定
  2. 场景分析 - 识别关键场景和转换点
  3. 分镜生成 - 创建详细的镜头脚本
  4. 优化润色 - 完善分镜细节和连贯性
  5. 最终输出 - 生成可执行的制作脚本
- **多语言支持**: 中英文智能翻译和本地化

### 🖼️ 图像生成系统
- **多引擎支持**: 
  - Pollinations (免费，快速)
  - ComfyUI (本地/云端，高质量)
  - DALL-E (OpenAI，高精度)
  - Stability AI (专业级)
  - Google Imagen (企业级)
- **智能路由**: 根据需求自动选择最佳引擎
- **批量生成**: 支持大规模图像批量处理
- **风格一致性**: 确保整个项目视觉风格统一

### 🎙️ 语音与音频处理
- **多平台TTS**: Azure、ElevenLabs、OpenAI、Edge TTS
- **语音识别**: 支持音频转文字功能
- **批量处理**: 自动为所有分镜生成配音
- **音效合成**: 背景音乐和音效自动添加

### 🎬 视频合成与后期
- **自动剪辑**: 根据分镜自动组合素材
- **转场效果**: 智能添加场景转换效果
- **字幕生成**: 自动生成和同步字幕
- **多格式输出**: MP4、AVI、MOV等格式支持

## ⚙️ 配置指南

### 基础配置
1. 复制配置模板：
```bash
cp config/app_settings.example.json config/app_settings.json
```

2. 编辑配置文件，添加你的API密钥：
```json
{
    "models": [
        {
            "name": "DeepSeek",
            "type": "deepseek",
            "key": "YOUR_DEEPSEEK_API_KEY",
            "url": "https://api.deepseek.com/v1/chat/completions"
        }
    ],
    "image_generation": {
        "default_engine": "pollinations",
        "dalle": {
            "api_key": "YOUR_OPENAI_API_KEY"
        }
    }
}
```

### LLM服务配置
支持的LLM提供商及其配置：

| 提供商 | 配置类型 | API地址 | 说明 |
|--------|----------|---------|------|
| DeepSeek | deepseek | https://api.deepseek.com/v1 | 性价比高，推荐 |
| 通义千问 | tongyi | https://dashscope.aliyuncs.com | 阿里云服务 |
| 智谱AI | zhipu | https://open.bigmodel.cn/api/paas/v4 | 国产优秀模型 |
| Google Gemini | google | https://generativelanguage.googleapis.com | Google服务 |
| OpenAI | openai | https://api.openai.com/v1 | 经典选择 |
| SiliconFlow | siliconflow | https://api.siliconflow.cn/v1 | 国内加速 |

### 图像生成配置
- **Pollinations**: 免费，无需配置，适合测试
- **ComfyUI**: 需要本地部署或云端服务
- **DALL-E**: 需要OpenAI API密钥
- **Stability AI**: 需要Stability AI API密钥

## 📖 使用教程

### 基础工作流程

#### 1. 创建新项目
- 启动程序后点击"新建项目"
- 输入项目名称和描述
- 选择输出目录

#### 2. 文本输入与处理
- 在"文本处理"标签页输入原始文本
- 选择处理风格（如"吉卜力风格"、"现实主义"等）
- 点击"开始处理"进行智能改写

#### 3. 五阶段分镜生成
- 切换到"五阶段分镜"标签页
- 选择LLM模型和风格
- 依次执行五个阶段：
  1. **世界观构建**: 分析文本背景设定
  2. **场景分析**: 识别关键场景
  3. **分镜生成**: 创建详细镜头脚本
  4. **优化润色**: 完善细节和连贯性
  5. **最终输出**: 生成制作脚本

#### 4. 图像生成
- 在"AI绘图"标签页选择生成引擎
- 可以单张生成或批量生成
- 支持风格一致性控制

#### 5. 一致性控制
- 使用"一致性控制"面板管理角色和场景
- 自动检测和维护视觉一致性
- 支持角色库和场景库管理

### 高级功能

#### 工作流编排
- 支持ComfyUI工作流导入
- 可视化工作流编辑
- 自定义处理流程

#### 批量处理
- 支持多文本批量处理
- 自动化生产流水线
- 进度监控和错误处理

#### 项目管理
- 完整的项目保存和加载
- 版本控制和历史记录
- 团队协作支持

## 🔧 开发指南

### 架构说明
程序采用分层架构设计：

1. **核心层 (Core)**:
   - `api_manager.py`: 统一API管理
   - `service_manager.py`: 服务生命周期管理
   - `service_base.py`: 服务基类定义

2. **服务层 (Services)**:
   - `llm_service.py`: LLM服务封装
   - `image_service.py`: 图像生成服务
   - `voice_service.py`: 语音服务

3. **处理器层 (Processors)**:
   - `text_processor.py`: 文本处理逻辑
   - `image_processor.py`: 图像处理逻辑
   - `video_processor.py`: 视频处理逻辑

4. **界面层 (GUI)**:
   - 基于PyQt5的现代化界面
   - 响应式设计和主题支持

### 扩展开发
- 添加新的AI服务提供商
- 自定义图像生成引擎
- 开发新的处理器模块
- 创建自定义界面组件

## 🧪 测试

### 运行测试
```bash
# 运行所有测试
python -m pytest tests/

# 运行特定测试
python -m pytest tests/test_service_manager.py

# 生成覆盖率报告
python -m pytest --cov=src tests/
```

### 性能测试
```bash
# 运行性能基准测试
python run_tests.py --benchmark

# 运行简单演示
python simple_demo.py
```

## 🐛 故障排除

### 常见问题

**Q: 程序启动失败，提示缺少模块**
A: 确保已安装所有依赖：`pip install -r requirements.txt`

**Q: API调用失败**
A: 检查API密钥配置和网络连接，查看日志文件获取详细错误信息

**Q: 图像生成速度慢**
A: 尝试切换到Pollinations引擎，或检查ComfyUI服务状态

**Q: 内存不足**
A: 减少批量处理数量，或升级硬件配置

### 日志查看
- 程序日志位于 `logs/` 目录
- 使用日志查看器：程序内置日志查看功能
- 调试模式：设置环境变量 `DEBUG=1`

## 🤝 贡献指南

欢迎贡献代码！请遵循以下步骤：

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 创建 Pull Request

### 开发规范
- 遵循PEP 8代码风格
- 添加适当的文档字符串
- 编写单元测试
- 更新相关文档

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

## 🙏 致谢

- 感谢所有AI服务提供商的支持
- 感谢开源社区的贡献
- 特别感谢测试用户的反馈

## 📞 联系方式

- 项目主页: [GitHub Repository](https://github.com/your-repo/AI_Video_Generator)
- 问题反馈: [Issues](https://github.com/your-repo/AI_Video_Generator/issues)
- 讨论交流: [Discussions](https://github.com/your-repo/AI_Video_Generator/discussions)

---

⭐ 如果这个项目对你有帮助，请给我们一个星标！
