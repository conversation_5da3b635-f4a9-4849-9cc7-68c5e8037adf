#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试所有模型的模型名称配置是否正确
"""

import requests
import json

def test_all_model_names():
    """测试所有LLM服务的模型名称"""
    print("=" * 70)
    print("所有LLM模型名称配置测试")
    print("=" * 70)
    
    # 从配置文件读取API配置
    try:
        with open('config/llm_config.json', 'r', encoding='utf-8') as f:
            config = json.load(f)
        models = config.get('models', [])
    except Exception as e:
        print(f"❌ 无法读取配置文件: {e}")
        return
    
    # 程序中实际使用的模型名称（来自LLMApi类）
    program_model_names = {
        'zhipu': 'glm-4-flash',
        'tongyi': 'qwen-plus', 
        'deepseek': 'deepseek-chat',
        'google': 'gemini-1.5-flash'
    }
    
    # 各服务支持的模型名称
    supported_models = {
        'zhipu': [
            'glm-4', 'glm-4-flash', 'glm-4-plus', 'glm-4-air', 
            'glm-4-airx', 'glm-4-long', 'glm-3-turbo'
        ],
        'tongyi': [
            'qwen-turbo', 'qwen-plus', 'qwen-max', 'qwen-max-1201',
            'qwen-max-longcontext', 'qwen-vl-plus', 'qwen-vl-max'
        ],
        'deepseek': [
            'deepseek-chat', 'deepseek-coder'
        ],
        'google': [
            'gemini-1.5-flash', 'gemini-1.5-pro', 'gemini-pro'
        ]
    }
    
    all_results = {}
    
    for model in models:
        name = model.get('name', '未知')
        api_type = model.get('type', '')
        url = model.get('url', '')
        key = model.get('key', '')
        
        print(f"\n🔍 测试 {name} ({api_type}):")
        print(f"   配置URL: {url}")
        
        # 检查程序使用的模型名称
        program_model = program_model_names.get(api_type, '未知')
        print(f"   程序使用模型: {program_model}")
        
        # 检查是否在支持列表中
        supported = supported_models.get(api_type, [])
        if program_model in supported:
            print(f"   ✅ 模型名称在支持列表中")
        else:
            print(f"   ⚠️  模型名称可能不在官方支持列表中")
            print(f"   支持的模型: {', '.join(supported)}")
        
        # 实际测试程序使用的模型名称
        print(f"   🧪 测试程序实际使用的模型名称...")
        
        headers = {
            'Content-Type': 'application/json',
            'User-Agent': 'AI-Video-Generator/1.0'
        }
        
        if api_type == 'google':
            test_data = {
                "contents": [{"parts": [{"text": "Hello"}]}]
            }
            test_url = url + f"?key={key}"
        else:
            test_data = {
                "model": program_model,
                "messages": [{"role": "user", "content": "Hello"}],
                "max_tokens": 10
            }
            test_url = url
            headers['Authorization'] = f"Bearer {key}"
        
        try:
            response = requests.post(
                test_url,
                json=test_data,
                headers=headers,
                timeout=30,
                proxies={"http": None, "https": None}
            )
            
            if response.status_code == 200:
                print(f"   ✅ 程序模型名称工作正常")
                all_results[name] = "正常"
            else:
                error_data = response.json() if response.headers.get('content-type', '').startswith('application/json') else {"error": response.text}
                error_msg = str(error_data)
                
                if "模型不存在" in error_msg or "model" in error_msg.lower() and "not" in error_msg.lower():
                    print(f"   ❌ 模型名称错误: {error_msg[:100]}...")
                    all_results[name] = "模型名称错误"
                    
                    # 尝试其他支持的模型名称
                    print(f"   🔄 尝试其他支持的模型名称...")
                    for alt_model in supported:
                        if alt_model != program_model:
                            print(f"      测试 {alt_model}...")
                            if api_type != 'google':
                                test_data['model'] = alt_model
                            
                            try:
                                alt_response = requests.post(
                                    test_url,
                                    json=test_data,
                                    headers=headers,
                                    timeout=15,
                                    proxies={"http": None, "https": None}
                                )
                                
                                if alt_response.status_code == 200:
                                    print(f"      ✅ {alt_model} 工作正常！建议使用此模型")
                                    all_results[name] = f"建议使用 {alt_model}"
                                    break
                                else:
                                    print(f"      ❌ {alt_model} 也失败")
                            except:
                                print(f"      ❌ {alt_model} 测试异常")
                elif "欠费" in error_msg:
                    print(f"   💰 账户欠费")
                    all_results[name] = "账户欠费"
                elif "location" in error_msg.lower() and "not supported" in error_msg.lower():
                    print(f"   🌍 地区不支持")
                    all_results[name] = "地区不支持"
                else:
                    print(f"   ⚠️  其他错误: {error_msg[:100]}...")
                    all_results[name] = "其他错误"
                    
        except Exception as e:
            print(f"   ❌ 测试异常: {e}")
            all_results[name] = "测试异常"
    
    # 生成汇总报告
    print("\n" + "=" * 70)
    print("模型配置汇总报告")
    print("=" * 70)
    
    for model_name, status in all_results.items():
        if status == "正常":
            print(f"✅ {model_name}: {status}")
        elif "建议使用" in status:
            print(f"🔄 {model_name}: {status}")
        else:
            print(f"❌ {model_name}: {status}")
    
    # 提供修复建议
    print("\n" + "=" * 70)
    print("修复建议")
    print("=" * 70)
    
    need_fix = [name for name, status in all_results.items() if status not in ["正常", "账户欠费", "地区不支持"]]
    
    if need_fix:
        print("需要修复模型名称配置的服务:")
        for name in need_fix:
            status = all_results[name]
            if "建议使用" in status:
                suggested_model = status.replace("建议使用 ", "")
                print(f"• {name}: 将程序中的模型名称改为 '{suggested_model}'")
    else:
        print("✅ 所有模型配置都正常，无需修复")

if __name__ == "__main__":
    test_all_model_names()
