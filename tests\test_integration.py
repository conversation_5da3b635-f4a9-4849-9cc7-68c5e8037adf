#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
集成测试

测试各个模块之间的集成和协作
"""

import unittest
import asyncio
import tempfile
import shutil
from pathlib import Path
from unittest.mock import Mock, patch, AsyncMock
from tests import BaseTestCase

from core.service_manager import ServiceManager, ServiceType
from utils.config_manager import ConfigManager, EnhancedConfigManager
from utils.logger import EnhancedLogger, LogConfig, LogLevel
from utils.error_handler import ErrorHandler


class TestSystemIntegration(BaseTestCase):
    """系统集成测试类"""
    
    def setUp(self):
        """测试设置"""
        super().setUp()
        
        # 创建临时配置目录
        self.temp_config_dir = self.temp_dir / "integration_config"
        self.temp_config_dir.mkdir(exist_ok=True)
        
        # 初始化组件
        self.config_manager = ConfigManager(str(self.temp_config_dir))
        self.enhanced_config = EnhancedConfigManager(str(self.temp_config_dir))
        self.logger = EnhancedLogger(LogConfig(name="IntegrationTest"))
        self.error_handler = ErrorHandler()
        self.service_manager = ServiceManager(self.config_manager)
    
    def tearDown(self):
        """测试清理"""
        super().tearDown()
        # 清理服务管理器任务
        for task in self.service_manager.running_tasks.values():
            if not task.done():
                task.cancel()
    
    def test_config_and_service_integration(self):
        """测试配置管理器与服务管理器集成"""
        # 添加测试模型配置
        self.config_manager.save_model_config(
            "TestIntegrationModel",
            "openai",
            "test_api_key",
            "https://api.test.com"
        )
        
        # 验证服务管理器可以访问配置
        model_config = self.config_manager.get_model_config("TestIntegrationModel")
        self.assertIsNotNone(model_config)
        
        # 验证API管理器可以使用配置
        api_configs = self.service_manager.api_manager.get_available_apis(ServiceType.LLM)
        # 注意：这里可能需要根据实际的API管理器实现调整
        self.assertIsInstance(api_configs, list)
    
    def test_logger_and_error_handler_integration(self):
        """测试日志系统与错误处理系统集成"""
        # 创建一个会产生错误的操作
        test_error = ValueError("Integration test error")
        
        # 使用错误处理器处理错误
        error_info = self.error_handler.handle_exception(
            test_error,
            context={"test": "integration"},
            show_to_user=False
        )
        
        # 验证错误信息
        self.assertIsNotNone(error_info)
        self.assertEqual(error_info.exception_type, "ValueError")
        
        # 使用日志记录器记录结构化日志
        self.logger.structured_log(
            LogLevel.ERROR,
            "integration_test_error",
            error_type=error_info.exception_type,
            error_category=error_info.category.value if error_info.category else None
        )
        
        # 验证日志统计
        stats = self.logger.get_stats()
        self.assertGreater(stats['total_logs'], 0)
    
    async def test_service_workflow_integration(self):
        """测试服务工作流集成"""
        # 创建测试工作流
        workflow_name = self.service_manager.create_video_generation_workflow(
            text="集成测试文本",
            style="测试风格"
        )
        
        # 模拟服务执行
        with patch.object(self.service_manager, 'execute_service_method') as mock_execute:
            from core.service_base import ServiceResult
            mock_execute.return_value = ServiceResult(
                success=True,
                data="集成测试结果",
                message="执行成功"
            )
            
            # 执行工作流
            results = await self.service_manager.execute_workflow(workflow_name)
            
            # 验证结果
            self.assertIsInstance(results, dict)
            self.assertTrue(len(results) > 0)
            
            # 验证所有步骤都成功
            for step_id, result in results.items():
                self.assertTrue(result.success)
    
    def test_enhanced_config_with_validation(self):
        """测试增强配置管理器的验证功能"""
        from utils.config_manager import ConfigSchema
        
        # 注册测试配置模式
        test_schema = ConfigSchema(
            name="integration_test",
            required_fields=["service_name", "api_key"],
            validators={
                "service_name": lambda x: isinstance(x, str) and len(x) > 0,
                "api_key": lambda x: isinstance(x, str) and len(x) >= 10
            }
        )
        
        self.enhanced_config.register_schema(test_schema)
        
        # 测试有效配置
        valid_config = {
            "service_name": "test_service",
            "api_key": "YOUR_API_KEY_HERE"
        }
        
        result = self.enhanced_config.set_config("integration_test", valid_config)
        self.assertTrue(result)
        
        # 测试无效配置
        invalid_config = {
            "service_name": "",  # 无效：空字符串
            "api_key": "INVALID_KEY"   # 无效：测试用
        }
        
        result = self.enhanced_config.set_config("integration_test", invalid_config)
        self.assertFalse(result)
    
    def test_performance_monitoring_integration(self):
        """测试性能监控集成"""
        import time
        
        # 模拟一个操作
        start_time = time.time()
        time.sleep(0.1)  # 模拟耗时操作
        duration = time.time() - start_time
        
        # 记录性能日志
        self.logger.performance_log(
            operation="integration_test_operation",
            duration=duration,
            test_data="integration_test"
        )
        
        # 验证性能日志被记录
        stats = self.logger.get_stats()
        self.assertGreater(stats['total_logs'], 0)
    
    def test_error_recovery_workflow(self):
        """测试错误恢复工作流"""
        # 模拟网络错误
        network_error = ConnectionError("Network connection failed")
        
        # 处理错误
        error_info = self.error_handler.handle_exception(
            network_error,
            context={"operation": "api_call", "retry_count": 0},
            show_to_user=False
        )
        
        # 验证错误分类
        from utils.error_handler import ErrorCategory
        self.assertEqual(error_info.category, ErrorCategory.NETWORK)
        
        # 验证解决方案
        self.assertIsInstance(error_info.solutions, list)
        self.assertTrue(len(error_info.solutions) > 0)
        
        # 记录错误恢复日志
        self.logger.structured_log(
            LogLevel.WARNING,
            "error_recovery_attempt",
            error_type=error_info.exception_type,
            solutions_count=len(error_info.solutions),
            retry_recommended=True
        )
    
    async def test_async_service_coordination(self):
        """测试异步服务协调"""
        # 创建多个异步任务
        async def mock_llm_task():
            await asyncio.sleep(0.1)
            return "LLM结果"
        
        async def mock_image_task():
            await asyncio.sleep(0.1)
            return "图像结果"
        
        async def mock_voice_task():
            await asyncio.sleep(0.1)
            return "语音结果"
        
        # 启动并发任务
        tasks = [
            self.service_manager.start_async_task("llm_task", mock_llm_task()),
            self.service_manager.start_async_task("image_task", mock_image_task()),
            self.service_manager.start_async_task("voice_task", mock_voice_task())
        ]
        
        # 等待所有任务完成
        results = await asyncio.gather(*tasks)
        
        # 验证结果
        self.assertEqual(len(results), 3)
        self.assertIn("LLM结果", results)
        self.assertIn("图像结果", results)
        self.assertIn("语音结果", results)
    
    def test_configuration_hot_reload(self):
        """测试配置热重载"""
        # 设置初始配置
        initial_config = {"setting": "initial_value"}
        self.enhanced_config.set_config("hot_reload_test", initial_config)
        
        # 验证初始配置
        config = self.enhanced_config.get_config("hot_reload_test")
        self.assertEqual(config["setting"], "initial_value")
        
        # 模拟配置文件更改
        updated_config = {"setting": "updated_value"}
        self.enhanced_config.set_config("hot_reload_test", updated_config)
        
        # 验证配置已更新
        config = self.enhanced_config.get_config("hot_reload_test")
        self.assertEqual(config["setting"], "updated_value")
    
    def test_system_health_check(self):
        """测试系统健康检查"""
        # 检查各个组件的状态
        
        # 1. 配置管理器状态
        models = self.config_manager.get_models()
        self.assertIsInstance(models, list)
        
        # 2. 服务管理器状态
        service_status = self.service_manager.get_service_status()
        self.assertIsInstance(service_status, dict)
        self.assertIn('services', service_status)
        
        # 3. 日志系统状态
        logger_stats = self.logger.get_stats()
        self.assertIsInstance(logger_stats, dict)
        self.assertIn('total_logs', logger_stats)
        
        # 4. 错误处理器状态
        error_stats = self.error_handler.get_error_statistics()
        self.assertIsInstance(error_stats, dict)
        
        # 记录健康检查结果
        self.logger.structured_log(
            LogLevel.INFO,
            "system_health_check",
            config_models_count=len(models),
            service_count=len(service_status.get('services', {})),
            total_logs=logger_stats['total_logs'],
            total_errors=error_stats.get('total_errors', 0)
        )


class TestEndToEndWorkflow(BaseTestCase):
    """端到端工作流测试"""
    
    def setUp(self):
        """测试设置"""
        super().setUp()
        self.temp_config_dir = self.temp_dir / "e2e_config"
        self.temp_config_dir.mkdir(exist_ok=True)
        
        # 创建完整的系统实例
        self.config_manager = ConfigManager(str(self.temp_config_dir))
        self.service_manager = ServiceManager(self.config_manager)
    
    async def test_complete_video_generation_workflow(self):
        """测试完整的视频生成工作流"""
        # 1. 配置设置
        self.config_manager.save_model_config(
            "E2ETestModel",
            "openai",
            "test_key_for_e2e",
            "https://api.test-e2e.com"
        )
        
        # 2. 创建工作流
        workflow_name = self.service_manager.create_video_generation_workflow(
            text="端到端测试：创建一个关于AI的短视频",
            style="科技风格"
        )
        
        # 3. 模拟服务执行
        with patch.object(self.service_manager, 'execute_service_method') as mock_execute:
            from core.service_base import ServiceResult
            
            # 模拟不同服务的响应
            def mock_service_response(*args, **kwargs):
                service_type = args[0] if args else None
                method = args[1] if len(args) > 1 else None
                
                if service_type == ServiceType.LLM and method == "generate_storyboard":
                    return ServiceResult(
                        success=True,
                        data="分镜脚本：镜头1-AI概念介绍，镜头2-技术展示",
                        metadata={"provider": "test_llm"}
                    )
                elif service_type == ServiceType.IMAGE and method == "generate_image":
                    return ServiceResult(
                        success=True,
                        data="生成的图像路径",
                        metadata={"provider": "test_image"}
                    )
                elif service_type == ServiceType.VOICE and method == "text_to_speech":
                    return ServiceResult(
                        success=True,
                        data="生成的音频路径",
                        metadata={"provider": "test_voice"}
                    )
                else:
                    return ServiceResult(success=False, error="未知服务")
            
            mock_execute.side_effect = mock_service_response
            
            # 4. 执行工作流
            results = await self.service_manager.execute_workflow(workflow_name)
            
            # 5. 验证结果
            self.assertIsInstance(results, dict)
            self.assertTrue(len(results) > 0)
            
            # 验证每个步骤都成功
            for step_id, result in results.items():
                self.assertTrue(result.success, f"步骤 {step_id} 失败: {result.error}")
            
            # 验证调用了正确的服务方法
            self.assertTrue(mock_execute.called)
            self.assertGreater(mock_execute.call_count, 0)


def run_async_test(coro):
    """运行异步测试的辅助函数"""
    loop = asyncio.new_event_loop()
    asyncio.set_event_loop(loop)
    try:
        return loop.run_until_complete(coro)
    finally:
        loop.close()


if __name__ == '__main__':
    unittest.main()
