#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试运行脚本

提供便捷的测试执行入口
"""

import sys
import os
import subprocess
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))
sys.path.insert(0, str(project_root / "src"))


def install_test_dependencies():
    """安装测试依赖"""
    print("安装测试依赖...")
    try:
        subprocess.run([
            sys.executable, "-m", "pip", "install", "-r", "tests/requirements.txt"
        ], check=True)
        print("✓ 测试依赖安装完成")
        return True
    except subprocess.CalledProcessError as e:
        print(f"✗ 安装测试依赖失败: {e}")
        return False


def run_unit_tests():
    """运行单元测试"""
    print("\n" + "="*60)
    print("运行单元测试")
    print("="*60)
    
    try:
        # 使用自定义测试运行器
        from tests.test_runner import TestRunner
        
        runner = TestRunner(test_dir="tests", coverage_enabled=True)
        result = runner.run_tests(verbosity=2)
        
        # 生成报告
        output_dir = Path("test_output")
        output_dir.mkdir(exist_ok=True)
        
        runner.generate_coverage_report(str(output_dir / "coverage"))
        runner.generate_json_report(result, str(output_dir / "test_report.json"))
        
        print(f"\n测试完成 - 成功率: {result.success_rate:.1f}%")
        if result.coverage_percentage:
            print(f"代码覆盖率: {result.coverage_percentage:.1f}%")
        
        return result.failed == 0 and result.errors == 0
        
    except ImportError:
        print("使用标准unittest运行器...")
        return run_unittest_fallback()
    except Exception as e:
        print(f"运行测试时发生错误: {e}")
        return False


def run_unittest_fallback():
    """使用标准unittest作为fallback"""
    try:
        import unittest
        
        # 发现并运行测试
        loader = unittest.TestLoader()
        suite = loader.discover('tests', pattern='test_*.py')
        
        runner = unittest.TextTestRunner(verbosity=2)
        result = runner.run(suite)
        
        return result.wasSuccessful()
        
    except Exception as e:
        print(f"运行unittest失败: {e}")
        return False


def run_pytest():
    """使用pytest运行测试"""
    print("\n" + "="*60)
    print("使用pytest运行测试")
    print("="*60)
    
    try:
        # 检查pytest是否可用
        import pytest
        
        # 运行pytest
        exit_code = pytest.main([
            "tests/",
            "-v",
            "--tb=short",
            "--cov=src",
            "--cov-report=html:test_output/coverage_html",
            "--cov-report=xml:test_output/coverage.xml",
            "--cov-report=term-missing",
            "--cov-fail-under=70"
        ])
        
        return exit_code == 0
        
    except ImportError:
        print("pytest未安装，使用标准测试运行器...")
        return run_unit_tests()
    except Exception as e:
        print(f"运行pytest失败: {e}")
        return False


def run_specific_test(test_name):
    """运行特定测试"""
    print(f"\n运行特定测试: {test_name}")
    print("="*60)
    
    try:
        from tests.test_runner import TestRunner
        
        # 查找匹配的测试文件
        test_files = []
        test_dir = Path("tests")
        
        for test_file in test_dir.glob("test_*.py"):
            if test_name.lower() in test_file.name.lower():
                test_files.append(str(test_file))
        
        if not test_files:
            print(f"未找到匹配的测试文件: {test_name}")
            return False
        
        runner = TestRunner(test_dir="tests", coverage_enabled=True)
        result = runner.run_tests(test_files=test_files, verbosity=2)
        
        print(f"\n测试完成 - 成功率: {result.success_rate:.1f}%")
        return result.failed == 0 and result.errors == 0
        
    except Exception as e:
        print(f"运行特定测试失败: {e}")
        return False


def check_code_quality():
    """检查代码质量"""
    print("\n" + "="*60)
    print("检查代码质量")
    print("="*60)
    
    quality_checks = []
    
    # 检查flake8
    try:
        result = subprocess.run([
            sys.executable, "-m", "flake8", "src/", "--max-line-length=100"
        ], capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✓ flake8检查通过")
            quality_checks.append(True)
        else:
            print("✗ flake8检查失败:")
            print(result.stdout)
            quality_checks.append(False)
    except FileNotFoundError:
        print("- flake8未安装，跳过检查")
    
    # 检查black格式
    try:
        result = subprocess.run([
            sys.executable, "-m", "black", "--check", "src/"
        ], capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✓ black格式检查通过")
            quality_checks.append(True)
        else:
            print("✗ black格式检查失败:")
            print(result.stdout)
            quality_checks.append(False)
    except FileNotFoundError:
        print("- black未安装，跳过检查")
    
    return all(quality_checks) if quality_checks else True


def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description="AI视频生成器测试运行脚本")
    parser.add_argument("--install-deps", action="store_true", help="安装测试依赖")
    parser.add_argument("--pytest", action="store_true", help="使用pytest运行测试")
    parser.add_argument("--unittest", action="store_true", help="使用unittest运行测试")
    parser.add_argument("--test", help="运行特定测试")
    parser.add_argument("--quality", action="store_true", help="检查代码质量")
    parser.add_argument("--all", action="store_true", help="运行所有检查")
    
    args = parser.parse_args()
    
    success = True
    
    # 安装依赖
    if args.install_deps or args.all:
        if not install_test_dependencies():
            success = False
    
    # 运行特定测试
    if args.test:
        if not run_specific_test(args.test):
            success = False
    # 使用pytest
    elif args.pytest:
        if not run_pytest():
            success = False
    # 使用unittest
    elif args.unittest:
        if not run_unit_tests():
            success = False
    # 运行所有测试
    elif args.all or len(sys.argv) == 1:
        # 尝试使用pytest，如果失败则使用unittest
        if not run_pytest():
            print("\npytest失败，尝试使用unittest...")
            if not run_unit_tests():
                success = False
    
    # 检查代码质量
    if args.quality or args.all:
        if not check_code_quality():
            success = False
    
    # 输出最终结果
    print("\n" + "="*60)
    if success:
        print("✓ 所有检查通过！")
        print("测试报告位置: test_output/")
    else:
        print("✗ 部分检查失败，请查看上面的详细信息")
    print("="*60)
    
    sys.exit(0 if success else 1)


if __name__ == "__main__":
    main()
