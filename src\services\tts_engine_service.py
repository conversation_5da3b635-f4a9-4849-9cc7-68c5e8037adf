#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
TTS引擎服务层
支持多种配音引擎：Edge-TTS、CosyVoice、TTSMaker、科大讯飞、ElevenLabs
"""

import os
import asyncio
import json
import requests
import subprocess
from typing import Dict, Any, Optional, List, Union
from pathlib import Path
from abc import ABC, abstractmethod

from src.utils.logger import logger
from src.utils.config_manager import ConfigManager

# 尝试导入Edge TTS
try:
    import edge_tts
    from edge_tts import SubMaker
    EDGE_TTS_AVAILABLE = True
except ImportError:
    EDGE_TTS_AVAILABLE = False
    edge_tts = None
    SubMaker = None


class TTSEngineBase(ABC):
    """TTS引擎基类"""
    
    def __init__(self, config_manager: ConfigManager):
        self.config_manager = config_manager
        self.engine_name = self.__class__.__name__.replace('Engine', '').lower()
    
    @abstractmethod
    async def generate_speech(self, text: str, output_path: str, **kwargs) -> Dict[str, Any]:
        """生成语音
        
        Args:
            text: 要转换的文本
            output_path: 输出文件路径
            **kwargs: 引擎特定参数
            
        Returns:
            Dict[str, Any]: 生成结果
        """
        pass
    
    @abstractmethod
    def get_available_voices(self) -> List[Dict[str, str]]:
        """获取可用音色列表"""
        pass
    
    @abstractmethod
    def test_connection(self) -> Dict[str, Any]:
        """测试引擎连接"""
        pass
    
    def get_default_settings(self) -> Dict[str, Any]:
        """获取默认设置"""
        return {
            'voice': '',
            'speed': 1.0,
            'pitch': 0,
            'volume': 1.0,
            'language': 'zh-CN'
        }


class EdgeTTSEngine(TTSEngineBase):
    """Edge-TTS引擎"""
    
    def __init__(self, config_manager: ConfigManager):
        super().__init__(config_manager)
        self.voices_cache = None
    
    async def generate_speech(self, text: str, output_path: str, **kwargs) -> Dict[str, Any]:
        """使用Edge-TTS生成语音"""
        try:
            if not EDGE_TTS_AVAILABLE or edge_tts is None:
                return {
                    'success': False,
                    'error': 'Edge-TTS未安装，请运行: pip install edge-tts'
                }
            
            voice = kwargs.get('voice', 'zh-CN-YunxiNeural')
            rate = kwargs.get('speed', 1.0)
            pitch = kwargs.get('pitch', 0)
            volume = kwargs.get('volume', 1.0)
            
            # 确保输出目录存在
            os.makedirs(os.path.dirname(output_path), exist_ok=True)
            
            # 构建SSML
            rate_str = f"{int((rate - 1) * 100):+d}%"
            pitch_str = f"{pitch:+d}Hz"
            volume_str = f"{int(volume * 100)}%"
            
            ssml = f"""
            <speak version="1.0" xmlns="http://www.w3.org/2001/10/synthesis" xml:lang="zh-CN">
                <voice name="{voice}">
                    <prosody rate="{rate_str}" pitch="{pitch_str}" volume="{volume_str}">
                        {text}
                    </prosody>
                </voice>
            </speak>
            """
            
            # 生成语音
            communicate = edge_tts.Communicate(ssml, voice)
            await communicate.save(output_path)
            
            # 生成字幕（如果需要）
            subtitle_path = None
            if kwargs.get('generate_subtitles', False):
                subtitle_path = output_path.replace('.mp3', '.vtt')
                sub_maker = SubMaker()
                async for chunk in communicate.stream():
                    if chunk["type"] == "audio":
                        sub_maker.write(chunk["data"])
                    elif chunk["type"] == "WordBoundary":
                        sub_maker.create_sub((chunk["offset"], chunk["duration"]), chunk["text"])
                
                with open(subtitle_path, 'w', encoding='utf-8') as f:
                    f.write(sub_maker.generate_subs())
            
            return {
                'success': True,
                'output_path': output_path,
                'subtitle_path': subtitle_path,
                'engine': 'edge_tts',
                'voice': voice,
                'duration': None  # Edge-TTS不直接提供时长信息
            }
            
        except Exception as e:
            logger.error(f"Edge-TTS生成语音失败: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def get_available_voices(self) -> List[Dict[str, str]]:
        """获取Edge-TTS可用音色"""
        if self.voices_cache is not None:
            return self.voices_cache
        
        try:
            if not EDGE_TTS_AVAILABLE:
                return []
            
            # 这里应该异步获取，但为了简化，返回常用的中文音色
            voices = [
                {'name': 'zh-CN-YunxiNeural', 'display_name': '云希 (男)', 'language': 'zh-CN', 'gender': 'Male'},
                {'name': 'zh-CN-XiaoxiaoNeural', 'display_name': '晓晓 (女)', 'language': 'zh-CN', 'gender': 'Female'},
                {'name': 'zh-CN-YunyangNeural', 'display_name': '云扬 (男)', 'language': 'zh-CN', 'gender': 'Male'},
                {'name': 'zh-CN-XiaoyiNeural', 'display_name': '晓伊 (女)', 'language': 'zh-CN', 'gender': 'Female'},
                {'name': 'zh-CN-YunjianNeural', 'display_name': '云健 (男)', 'language': 'zh-CN', 'gender': 'Male'},
                {'name': 'zh-CN-XiaochenNeural', 'display_name': '晓辰 (女)', 'language': 'zh-CN', 'gender': 'Female'},
                {'name': 'zh-CN-XiaohanNeural', 'display_name': '晓涵 (女)', 'language': 'zh-CN', 'gender': 'Female'},
                {'name': 'zh-CN-XiaomengNeural', 'display_name': '晓梦 (女)', 'language': 'zh-CN', 'gender': 'Female'},
                {'name': 'zh-CN-XiaomoNeural', 'display_name': '晓墨 (女)', 'language': 'zh-CN', 'gender': 'Female'},
                {'name': 'zh-CN-XiaoqiuNeural', 'display_name': '晓秋 (女)', 'language': 'zh-CN', 'gender': 'Female'},
                {'name': 'zh-CN-XiaoruiNeural', 'display_name': '晓睿 (女)', 'language': 'zh-CN', 'gender': 'Female'},
                {'name': 'zh-CN-XiaoshuangNeural', 'display_name': '晓双 (女)', 'language': 'zh-CN', 'gender': 'Female'},
                {'name': 'zh-CN-XiaoxuanNeural', 'display_name': '晓萱 (女)', 'language': 'zh-CN', 'gender': 'Female'},
                {'name': 'zh-CN-XiaoyanNeural', 'display_name': '晓颜 (女)', 'language': 'zh-CN', 'gender': 'Female'},
                {'name': 'zh-CN-XiaoyouNeural', 'display_name': '晓悠 (女)', 'language': 'zh-CN', 'gender': 'Female'},
                {'name': 'zh-CN-XiaozhenNeural', 'display_name': '晓甄 (女)', 'language': 'zh-CN', 'gender': 'Female'},
                {'name': 'zh-CN-YunfengNeural', 'display_name': '云枫 (男)', 'language': 'zh-CN', 'gender': 'Male'},
                {'name': 'zh-CN-YunhaoNeural', 'display_name': '云皓 (男)', 'language': 'zh-CN', 'gender': 'Male'},
                {'name': 'zh-CN-YunzeNeural', 'display_name': '云泽 (男)', 'language': 'zh-CN', 'gender': 'Male'}
            ]
            
            self.voices_cache = voices
            return voices
            
        except Exception as e:
            logger.error(f"获取Edge-TTS音色列表失败: {e}")
            return []
    
    def test_connection(self) -> Dict[str, Any]:
        """测试Edge-TTS连接"""
        try:
            if not EDGE_TTS_AVAILABLE:
                return {
                    'success': False,
                    'error': 'Edge-TTS未安装，请运行: pip install edge-tts'
                }
            return {
                'success': True,
                'message': 'Edge-TTS可用'
            }
        except Exception as e:
            return {
                'success': False,
                'error': str(e)
            }
    
    def get_default_settings(self) -> Dict[str, Any]:
        """获取Edge-TTS默认设置"""
        return {
            'voice': 'zh-CN-YunxiNeural',
            'speed': 1.0,
            'pitch': 0,
            'volume': 1.0,
            'language': 'zh-CN'
        }


class CosyVoiceEngine(TTSEngineBase):
    """CosyVoice本地引擎"""

    def __init__(self, config_manager: ConfigManager):
        super().__init__(config_manager)
        model_path_setting = self.config_manager.get_setting('cosyvoice.model_path', '')
        self.model_path: str = str(model_path_setting) if model_path_setting else ''
    
    async def generate_speech(self, text: str, output_path: str, **kwargs) -> Dict[str, Any]:
        """使用CosyVoice生成语音"""
        try:
            if not self.model_path or not os.path.exists(self.model_path):
                return {
                    'success': False,
                    'error': 'CosyVoice模型路径未配置或不存在'
                }
            
            voice = kwargs.get('voice', 'default')
            speed = kwargs.get('speed', 1.0)
            
            # 确保输出目录存在
            os.makedirs(os.path.dirname(output_path), exist_ok=True)
            
            # 构建CosyVoice命令
            cmd = [
                'python', 
                os.path.join(self.model_path, 'inference.py'),
                '--text', text,
                '--output', output_path,
                '--voice', voice,
                '--speed', str(speed)
            ]
            
            # 执行命令
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=300)
            
            if result.returncode == 0:
                return {
                    'success': True,
                    'output_path': output_path,
                    'engine': 'cosyvoice',
                    'voice': voice
                }
            else:
                return {
                    'success': False,
                    'error': f'CosyVoice执行失败: {result.stderr}'
                }
                
        except subprocess.TimeoutExpired:
            return {
                'success': False,
                'error': 'CosyVoice执行超时'
            }
        except Exception as e:
            logger.error(f"CosyVoice生成语音失败: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def get_available_voices(self) -> List[Dict[str, str]]:
        """获取CosyVoice可用音色"""
        return [
            {'name': 'default', 'display_name': '默认音色', 'language': 'zh-CN', 'gender': 'Unknown'},
            {'name': 'female1', 'display_name': '女声1', 'language': 'zh-CN', 'gender': 'Female'},
            {'name': 'male1', 'display_name': '男声1', 'language': 'zh-CN', 'gender': 'Male'}
        ]

    def test_connection(self) -> Dict[str, Any]:
        """测试CosyVoice连接"""
        try:
            if not self.model_path:
                return {
                    'success': False,
                    'error': 'CosyVoice模型路径未配置'
                }

            if not os.path.exists(self.model_path):
                return {
                    'success': False,
                    'error': f'CosyVoice模型路径不存在: {self.model_path}'
                }

            # 检查inference.py是否存在
            inference_path = os.path.join(self.model_path, 'inference.py')
            if not os.path.exists(inference_path):
                return {
                    'success': False,
                    'error': f'CosyVoice推理脚本不存在: {inference_path}'
                }

            return {
                'success': True,
                'message': 'CosyVoice模型可用'
            }
        except Exception as e:
            return {
                'success': False,
                'error': str(e)
            }

    def get_default_settings(self) -> Dict[str, Any]:
        """获取CosyVoice默认设置"""
        return {
            'voice': 'default',
            'speed': 1.0,
            'pitch': 0,
            'volume': 1.0,
            'language': 'zh-CN',
            'model_path': self.model_path
        }


class TTSMakerEngine(TTSEngineBase):
    """TTSMaker引擎"""

    def __init__(self, config_manager: ConfigManager):
        super().__init__(config_manager)
        self.api_url = 'https://api.ttsmaker.com/v1/create-speech'
        self.api_key = self.config_manager.get_setting('ttsmaker.api_key', '')

    async def generate_speech(self, text: str, output_path: str, **kwargs) -> Dict[str, Any]:
        """使用TTSMaker生成语音"""
        try:
            if not self.api_key:
                return {
                    'success': False,
                    'error': 'TTSMaker API密钥未配置'
                }

            voice = kwargs.get('voice', 'zh-CN-XiaoxiaoNeural')
            speed = kwargs.get('speed', 1.0)

            # 构建请求数据
            data = {
                'text': text,
                'voice': voice,
                'speed': speed,
                'format': 'mp3'
            }

            headers = {
                'Authorization': f'Bearer {self.api_key}',
                'Content-Type': 'application/json'
            }

            # 发送请求
            response = requests.post(self.api_url, json=data, headers=headers, timeout=60)

            if response.status_code == 200:
                # 确保输出目录存在
                os.makedirs(os.path.dirname(output_path), exist_ok=True)

                # 保存音频文件
                with open(output_path, 'wb') as f:
                    f.write(response.content)

                return {
                    'success': True,
                    'output_path': output_path,
                    'engine': 'ttsmaker',
                    'voice': voice
                }
            else:
                return {
                    'success': False,
                    'error': f'TTSMaker API请求失败: {response.status_code} - {response.text}'
                }

        except Exception as e:
            logger.error(f"TTSMaker生成语音失败: {e}")
            return {
                'success': False,
                'error': str(e)
            }

    def get_available_voices(self) -> List[Dict[str, str]]:
        """获取TTSMaker可用音色"""
        return [
            {'name': 'zh-CN-XiaoxiaoNeural', 'display_name': '晓晓', 'language': 'zh-CN', 'gender': 'Female'},
            {'name': 'zh-CN-YunxiNeural', 'display_name': '云希', 'language': 'zh-CN', 'gender': 'Male'},
            {'name': 'zh-CN-YunyangNeural', 'display_name': '云扬', 'language': 'zh-CN', 'gender': 'Male'}
        ]

    def test_connection(self) -> Dict[str, Any]:
        """测试TTSMaker连接"""
        try:
            if not self.api_key:
                return {
                    'success': False,
                    'error': 'TTSMaker API密钥未配置'
                }

            # 简单的连接测试
            headers = {
                'Authorization': f'Bearer {self.api_key}',
                'Content-Type': 'application/json'
            }

            test_data = {
                'text': '测试',
                'voice': 'zh-CN-XiaoxiaoNeural',
                'format': 'mp3'
            }

            response = requests.post(self.api_url, json=test_data, headers=headers, timeout=10)

            if response.status_code in [200, 400]:  # 400可能是参数错误，但说明连接正常
                return {
                    'success': True,
                    'message': 'TTSMaker API连接正常'
                }
            else:
                return {
                    'success': False,
                    'error': f'TTSMaker API连接失败: {response.status_code}'
                }

        except Exception as e:
            return {
                'success': False,
                'error': str(e)
            }

    def get_default_settings(self) -> Dict[str, Any]:
        """获取TTSMaker默认设置"""
        return {
            'voice': 'zh-CN-XiaoxiaoNeural',
            'speed': 1.0,
            'pitch': 0,
            'volume': 1.0,
            'language': 'zh-CN',
            'api_key': self.api_key
        }


class XunfeiEngine(TTSEngineBase):
    """科大讯飞引擎"""

    def __init__(self, config_manager: ConfigManager):
        super().__init__(config_manager)
        self.app_id = self.config_manager.get_setting('xunfei.app_id', '')
        self.api_key = self.config_manager.get_setting('xunfei.api_key', '')
        self.api_secret = self.config_manager.get_setting('xunfei.api_secret', '')
        self.api_url = 'https://tts-api.xfyun.cn/v2/tts'

    async def generate_speech(self, text: str, output_path: str, **kwargs) -> Dict[str, Any]:
        """使用科大讯飞生成语音"""
        return {
            'success': False,
            'error': '科大讯飞TTS功能暂未实现'
        }

    def get_available_voices(self) -> List[Dict[str, str]]:
        """获取科大讯飞可用音色"""
        return [
            {'name': 'xiaoyan', 'display_name': '小燕', 'language': 'zh-CN', 'gender': 'Female'},
            {'name': 'xiaoyu', 'display_name': '小宇', 'language': 'zh-CN', 'gender': 'Male'}
        ]

    def test_connection(self) -> Dict[str, Any]:
        """测试科大讯飞连接"""
        if not all([self.app_id, self.api_key, self.api_secret]):
            return {
                'success': False,
                'error': '科大讯飞API配置不完整'
            }
        return {
            'success': True,
            'message': '科大讯飞配置完整（功能暂未实现）'
        }

    def get_default_settings(self) -> Dict[str, Any]:
        """获取科大讯飞默认设置"""
        return {
            'voice': 'xiaoyan',
            'speed': 1.0,
            'pitch': 0,
            'volume': 1.0,
            'language': 'zh-CN',
            'app_id': self.app_id,
            'api_key': self.api_key,
            'api_secret': self.api_secret
        }


class ElevenLabsEngine(TTSEngineBase):
    """ElevenLabs引擎"""

    def __init__(self, config_manager: ConfigManager):
        super().__init__(config_manager)
        self.api_key = self.config_manager.get_setting('elevenlabs.api_key', '')
        self.api_url = 'https://api.elevenlabs.io/v1/text-to-speech'

    async def generate_speech(self, text: str, output_path: str, **kwargs) -> Dict[str, Any]:
        """使用ElevenLabs生成语音"""
        return {
            'success': False,
            'error': 'ElevenLabs TTS功能暂未实现'
        }

    def get_available_voices(self) -> List[Dict[str, str]]:
        """获取ElevenLabs可用音色"""
        return [
            {'name': 'rachel', 'display_name': 'Rachel', 'language': 'en-US', 'gender': 'Female'},
            {'name': 'drew', 'display_name': 'Drew', 'language': 'en-US', 'gender': 'Male'}
        ]

    def test_connection(self) -> Dict[str, Any]:
        """测试ElevenLabs连接"""
        if not self.api_key:
            return {
                'success': False,
                'error': 'ElevenLabs API密钥未配置'
            }
        return {
            'success': True,
            'message': 'ElevenLabs配置完整（功能暂未实现）'
        }

    def get_default_settings(self) -> Dict[str, Any]:
        """获取ElevenLabs默认设置"""
        return {
            'voice': 'rachel',
            'speed': 1.0,
            'pitch': 0,
            'volume': 1.0,
            'language': 'en-US',
            'api_key': self.api_key
        }


class TTSEngineManager:
    """TTS引擎管理器"""

    def __init__(self, config_manager: ConfigManager):
        self.config_manager = config_manager
        self.engines = {}
        self._init_engines()

    def _init_engines(self):
        """初始化所有引擎"""
        self.engines = {
            'edge_tts': EdgeTTSEngine(self.config_manager),
            'cosyvoice': CosyVoiceEngine(self.config_manager),
            'ttsmaker': TTSMakerEngine(self.config_manager),
            'xunfei': XunfeiEngine(self.config_manager),
            'elevenlabs': ElevenLabsEngine(self.config_manager)
        }

    def get_engine(self, engine_name: str) -> Optional[TTSEngineBase]:
        """获取指定引擎"""
        return self.engines.get(engine_name)

    def get_available_engines(self) -> List[str]:
        """获取可用引擎列表"""
        return list(self.engines.keys())

    def get_engine_info(self, engine_name: str) -> Dict[str, Any]:
        """获取引擎信息"""
        engine = self.get_engine(engine_name)
        if not engine:
            return {'error': f'引擎 {engine_name} 不存在'}

        return {
            'name': engine_name,
            'voices': engine.get_available_voices(),
            'default_settings': engine.get_default_settings(),
            'connection_status': engine.test_connection()
        }

    async def generate_speech(self, engine_name: str, text: str, output_path: str, **kwargs) -> Dict[str, Any]:
        """使用指定引擎生成语音"""
        engine = self.get_engine(engine_name)
        if not engine:
            return {
                'success': False,
                'error': f'引擎 {engine_name} 不存在'
            }

        return await engine.generate_speech(text, output_path, **kwargs)

    def test_all_engines(self) -> Dict[str, Dict[str, Any]]:
        """测试所有引擎连接"""
        results = {}
        for engine_name, engine in self.engines.items():
            results[engine_name] = engine.test_connection()
        return results

    def get_voices_by_language(self, language: str = 'zh-CN') -> Dict[str, List[Dict[str, str]]]:
        """按语言获取所有引擎的音色"""
        voices_by_engine = {}
        for engine_name, engine in self.engines.items():
            voices = engine.get_available_voices()
            filtered_voices = [v for v in voices if v.get('language', '').startswith(language[:2])]
            if filtered_voices:
                voices_by_engine[engine_name] = filtered_voices
        return voices_by_engine
