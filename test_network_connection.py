#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试网络连接和API可用性
"""

import requests
import json
import time
import socket
from urllib.parse import urlparse

def test_basic_connectivity():
    """测试基本网络连接"""
    print("=" * 50)
    print("基本网络连接测试")
    print("=" * 50)
    
    # 测试DNS解析
    test_domains = [
        "www.baidu.com",
        "www.google.com", 
        "open.bigmodel.cn",
        "api.deepseek.com",
        "dashscope.aliyuncs.com",
        "generativelanguage.googleapis.com"
    ]
    
    for domain in test_domains:
        try:
            socket.gethostbyname(domain)
            print(f"✅ DNS解析成功: {domain}")
        except Exception as e:
            print(f"❌ DNS解析失败: {domain} - {e}")
    
    # 测试HTTP连接
    test_urls = [
        "https://www.baidu.com",
        "https://www.google.com",
        "https://httpbin.org/get"
    ]
    
    print("\nHTTP连接测试:")
    for url in test_urls:
        try:
            response = requests.get(url, timeout=10)
            print(f"✅ HTTP连接成功: {url} (状态码: {response.status_code})")
        except Exception as e:
            print(f"❌ HTTP连接失败: {url} - {e}")

def test_api_endpoints():
    """测试各个API端点的连接性"""
    print("\n" + "=" * 50)
    print("API端点连接测试")
    print("=" * 50)
    
    # 从配置文件读取API配置
    try:
        with open('config/llm_config.json', 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        models = config.get('models', [])
        
        for model in models:
            name = model.get('name', '未知')
            url = model.get('url', '')
            api_type = model.get('type', '')
            
            print(f"\n测试 {name} ({api_type}):")
            print(f"URL: {url}")
            
            # 解析URL获取基础域名
            parsed = urlparse(url)
            base_url = f"{parsed.scheme}://{parsed.netloc}"
            
            try:
                # 先测试基础连接
                response = requests.get(base_url, timeout=10)
                print(f"✅ 基础连接成功 (状态码: {response.status_code})")
            except Exception as e:
                print(f"❌ 基础连接失败: {e}")
                continue
            
            # 测试API端点（发送简单的POST请求）
            try:
                headers = {
                    'Content-Type': 'application/json',
                    'User-Agent': 'AI-Video-Generator/1.0'
                }
                
                # 构造一个简单的测试请求
                if api_type == 'google':
                    # Google API格式不同
                    test_data = {
                        "contents": [{"parts": [{"text": "Hello"}]}]
                    }
                    test_url = url + f"?key={model.get('key', '')}"
                else:
                    # OpenAI格式
                    test_data = {
                        "model": "gpt-3.5-turbo",
                        "messages": [{"role": "user", "content": "Hello"}],
                        "max_tokens": 10
                    }
                    test_url = url
                    headers['Authorization'] = f"Bearer {model.get('key', '')}"
                
                response = requests.post(test_url, json=test_data, headers=headers, timeout=30)
                
                if response.status_code == 200:
                    print(f"✅ API调用成功")
                elif response.status_code == 401:
                    print(f"⚠️  API密钥验证失败 (401) - 可能是密钥问题")
                elif response.status_code == 403:
                    print(f"⚠️  API访问被拒绝 (403) - 可能是权限问题")
                elif response.status_code == 429:
                    print(f"⚠️  API请求频率限制 (429)")
                else:
                    print(f"⚠️  API返回状态码: {response.status_code}")
                    print(f"响应内容: {response.text[:200]}...")
                    
            except requests.exceptions.Timeout:
                print(f"❌ API请求超时")
            except requests.exceptions.ConnectionError as e:
                print(f"❌ API连接错误: {e}")
            except Exception as e:
                print(f"❌ API测试失败: {e}")
                
    except Exception as e:
        print(f"❌ 读取配置文件失败: {e}")

def test_proxy_settings():
    """测试代理设置"""
    print("\n" + "=" * 50)
    print("代理设置测试")
    print("=" * 50)
    
    import os
    
    # 检查环境变量中的代理设置
    proxy_vars = ['HTTP_PROXY', 'HTTPS_PROXY', 'http_proxy', 'https_proxy']
    
    for var in proxy_vars:
        value = os.environ.get(var)
        if value:
            print(f"发现代理设置: {var} = {value}")
        else:
            print(f"未设置: {var}")
    
    # 测试无代理连接
    print("\n测试无代理连接:")
    try:
        response = requests.get("https://httpbin.org/ip", 
                              proxies={"http": None, "https": None}, 
                              timeout=10)
        print(f"✅ 无代理连接成功，IP: {response.json().get('origin', 'unknown')}")
    except Exception as e:
        print(f"❌ 无代理连接失败: {e}")

if __name__ == "__main__":
    print("网络连接和API可用性诊断工具")
    print("=" * 50)
    
    test_basic_connectivity()
    test_proxy_settings()
    test_api_endpoints()
    
    print("\n" + "=" * 50)
    print("诊断完成")
    print("=" * 50)
