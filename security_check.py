#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI视频生成器 - 安全检查脚本
检查项目中是否包含敏感信息
"""

import os
import re
import json
from pathlib import Path

def print_banner():
    """打印检查横幅"""
    print("=" * 60)
    print("AI视频生成器 - 安全检查工具")
    print("=" * 60)

def load_gitignore_patterns():
    """加载.gitignore模式"""
    patterns = []
    gitignore_path = Path(".gitignore")
    
    if gitignore_path.exists():
        with open(gitignore_path, 'r', encoding='utf-8') as f:
            for line in f:
                line = line.strip()
                if line and not line.startswith('#'):
                    patterns.append(line)
    
    return patterns

def is_ignored_by_gitignore(file_path, gitignore_patterns):
    """检查文件是否被.gitignore忽略"""
    file_path = str(file_path).replace('\\', '/')
    
    for pattern in gitignore_patterns:
        # 简单的模式匹配
        if pattern.endswith('/'):
            # 目录模式
            if file_path.startswith(pattern) or f"/{pattern}" in file_path:
                return True
        elif '*' in pattern:
            # 通配符模式
            pattern_regex = pattern.replace('*', '.*').replace('?', '.')
            if re.match(pattern_regex, file_path) or re.search(pattern_regex, file_path):
                return True
        else:
            # 精确匹配
            if file_path == pattern or file_path.endswith(f"/{pattern}"):
                return True
    
    return False

def check_api_key_patterns(content):
    """检查API密钥模式"""
    suspicious_patterns = [
        # OpenAI API密钥
        (r'sk-[a-zA-Z0-9]{48}', 'OpenAI API Key'),
        (r'sk-proj-[a-zA-Z0-9]{48}', 'OpenAI Project API Key'),

        # Google API密钥
        (r'AIza[0-9A-Za-z_-]{35}', 'Google API Key'),

        # AWS访问密钥
        (r'AKIA[0-9A-Z]{16}', 'AWS Access Key'),

        # 通用Bearer Token (排除文档中的示例)
        (r'Bearer [a-zA-Z0-9_-]{20,}', 'Bearer Token'),

        # 通用API密钥模式
        (r'api[_-]?key["\']?\s*[:=]\s*["\'][^"\']{10,}["\']', 'API Key'),
        (r'secret[_-]?key["\']?\s*[:=]\s*["\'][^"\']{10,}["\']', 'Secret Key'),
        (r'access[_-]?token["\']?\s*[:=]\s*["\'][^"\']{10,}["\']', 'Access Token'),

        # 其他敏感信息
        (r'password["\']?\s*[:=]\s*["\'][^"\']{5,}["\']', 'Password'),
    ]
    
    found_patterns = []
    
    for pattern, description in suspicious_patterns:
        matches = re.finditer(pattern, content, re.IGNORECASE)
        for match in matches:
            # 排除明显的示例值
            matched_text = match.group()
            if any(placeholder in matched_text.upper() for placeholder in [
                'YOUR_', 'EXAMPLE', 'PLACEHOLDER', 'DEMO', 'TEST', 'SAMPLE',
                'XXX', '***', '...', 'REPLACE', 'INSERT', 'ENTER', 'INVALID',
                'MONGODB://', 'MYSQL://', 'POSTGRESQL://', '[^', 'REGEX'
            ]):
                continue
            
            found_patterns.append({
                'pattern': description,
                'match': matched_text,
                'start': match.start(),
                'end': match.end()
            })
    
    return found_patterns

def scan_file(file_path):
    """扫描单个文件"""
    try:
        with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
            content = f.read()
        
        # 检查API密钥模式
        suspicious = check_api_key_patterns(content)
        
        return suspicious
        
    except Exception as e:
        return [{'pattern': 'File Read Error', 'match': str(e), 'start': 0, 'end': 0}]

def scan_project():
    """扫描整个项目"""
    print("扫描项目文件...")
    
    # 加载.gitignore模式
    gitignore_patterns = load_gitignore_patterns()
    
    # 要扫描的文件类型
    scan_extensions = {'.py', '.json', '.txt', '.md', '.yml', '.yaml', '.env', '.cfg', '.ini'}
    
    # 要跳过的目录
    skip_dirs = {'venv', '.git', '__pycache__', 'node_modules', '.pytest_cache'}
    
    results = {}
    total_files = 0
    scanned_files = 0
    
    for root, dirs, files in os.walk('.'):
        # 移除要跳过的目录
        dirs[:] = [d for d in dirs if d not in skip_dirs]
        
        for file in files:
            file_path = Path(root) / file
            total_files += 1
            
            # 检查文件扩展名
            if file_path.suffix.lower() not in scan_extensions:
                continue
            
            # 检查是否被.gitignore忽略
            if is_ignored_by_gitignore(file_path, gitignore_patterns):
                continue
            
            scanned_files += 1
            print(f"   扫描: {file_path}")
            
            # 扫描文件
            suspicious = scan_file(file_path)
            
            if suspicious:
                results[str(file_path)] = suspicious
    
    print(f"📊 扫描完成: {scanned_files}/{total_files} 个文件")
    return results

def check_config_files():
    """检查配置文件"""
    print("⚙️ 检查配置文件...")
    
    config_files = [
        'config/app_settings.json',
        'config/llm_config.json',
        'config/tts_config.json',
        'config/baidu_translate_config.py'
    ]
    
    issues = []
    
    for config_file in config_files:
        if os.path.exists(config_file):
            print(f"   ❌ 发现敏感配置文件: {config_file}")
            issues.append(f"敏感配置文件存在: {config_file}")
        else:
            print(f"   ✅ 敏感配置文件已排除: {config_file}")
    
    return issues

def check_example_files():
    """检查示例文件是否安全"""
    print("📋 检查示例文件...")
    
    example_files = [
        'config/app_settings.example.json',
        'config/llm_config.example.json',
        'config/tts_config.example.json',
        'config/baidu_translate_config.example.py'
    ]
    
    issues = []
    
    for example_file in example_files:
        if os.path.exists(example_file):
            print(f"   检查: {example_file}")
            suspicious = scan_file(example_file)
            if suspicious:
                print(f"   ⚠️ 示例文件包含可疑内容: {example_file}")
                issues.append(f"示例文件包含可疑内容: {example_file}")
            else:
                print(f"   ✅ 示例文件安全: {example_file}")
        else:
            print(f"   ⚠️ 示例文件不存在: {example_file}")
    
    return issues

def generate_security_report(scan_results, config_issues, example_issues):
    """生成安全报告"""
    print("\n" + "=" * 60)
    print("📋 安全检查报告")
    print("=" * 60)
    
    total_issues = len(scan_results) + len(config_issues) + len(example_issues)
    
    if total_issues == 0:
        print("🎉 安全检查通过！")
        print("✅ 未发现敏感信息")
        print("✅ 配置文件已正确排除")
        print("✅ 示例文件安全")
        return True
    
    print(f"⚠️ 发现 {total_issues} 个安全问题:")
    
    # 文件扫描结果
    if scan_results:
        print("\n🔍 文件扫描结果:")
        for file_path, issues in scan_results.items():
            print(f"   📄 {file_path}:")
            for issue in issues:
                print(f"      ❌ {issue['pattern']}: {issue['match'][:50]}...")
    
    # 配置文件问题
    if config_issues:
        print("\n⚙️ 配置文件问题:")
        for issue in config_issues:
            print(f"   ❌ {issue}")
    
    # 示例文件问题
    if example_issues:
        print("\n📋 示例文件问题:")
        for issue in example_issues:
            print(f"   ❌ {issue}")
    
    print("\n🔧 修复建议:")
    print("1. 移除或替换真实的API密钥")
    print("2. 确保敏感配置文件被.gitignore排除")
    print("3. 检查示例文件中的占位符")
    print("4. 运行 'git status' 确认文件状态")
    
    return False

def main():
    """主检查流程"""
    print_banner()
    
    print("开始安全检查...\n")
    
    # 扫描项目文件
    scan_results = scan_project()
    print()
    
    # 检查配置文件
    config_issues = check_config_files()
    print()
    
    # 检查示例文件
    example_issues = check_example_files()
    print()
    
    # 生成报告
    is_safe = generate_security_report(scan_results, config_issues, example_issues)
    
    if is_safe:
        print("\n✅ 项目可以安全上传到GitHub")
    else:
        print("\n❌ 请修复安全问题后再上传")
    
    return is_safe

if __name__ == "__main__":
    try:
        success = main()
        exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n❌ 安全检查被用户中断")
        exit(1)
    except Exception as e:
        print(f"\n❌ 安全检查过程中发生错误: {e}")
        exit(1)
